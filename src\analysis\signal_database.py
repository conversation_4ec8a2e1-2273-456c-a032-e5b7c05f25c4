#!/usr/bin/env python3
"""
信号数据库管理器 - 负责信号数据的持久化存储和查询
"""

import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path
import threading
import shutil


@dataclass
class SignalRecord:
    """信号记录数据结构"""
    id: Optional[int] = None
    agent_name: str = ""
    ticker: str = ""
    signal_date: str = ""
    signal_type: str = ""  # bullish, bearish, neutral
    confidence: float = 0.0
    reasoning: str = ""
    current_price: float = 0.0
    market_condition: str = ""
    created_at: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'agent_name': self.agent_name,
            'ticker': self.ticker,
            'signal_date': self.signal_date,
            'signal_type': self.signal_type,
            'confidence': self.confidence,
            'reasoning': self.reasoning,
            'current_price': self.current_price,
            'market_condition': self.market_condition,
            'created_at': self.created_at
        }


@dataclass
class AccuracyRecord:
    """准确性记录数据结构"""
    id: Optional[int] = None
    signal_id: int = 0
    evaluation_date: str = ""
    time_horizon_days: int = 0
    actual_price: float = 0.0
    price_change: float = 0.0
    price_change_pct: float = 0.0
    is_correct: bool = False
    accuracy_score: float = 0.0
    market_condition_at_eval: str = ""
    created_at: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'signal_id': self.signal_id,
            'evaluation_date': self.evaluation_date,
            'time_horizon_days': self.time_horizon_days,
            'actual_price': self.actual_price,
            'price_change': self.price_change,
            'price_change_pct': self.price_change_pct,
            'is_correct': self.is_correct,
            'accuracy_score': self.accuracy_score,
            'market_condition_at_eval': self.market_condition_at_eval,
            'created_at': self.created_at
        }


class SignalDatabase:
    """信号数据库管理器"""
    
    def __init__(self, db_path: str = "signal_tracking.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        
        # 确保数据库目录存在
        db_dir = Path(db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 创建信号表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS signals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        agent_name TEXT NOT NULL,
                        ticker TEXT NOT NULL,
                        signal_date TEXT NOT NULL,
                        signal_type TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        reasoning TEXT,
                        current_price REAL,
                        market_condition TEXT,
                        created_at TEXT NOT NULL,
                        UNIQUE(agent_name, ticker, signal_date)
                    )
                ''')
                
                # 创建准确性评估表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accuracy_evaluations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        signal_id INTEGER NOT NULL,
                        evaluation_date TEXT NOT NULL,
                        time_horizon_days INTEGER NOT NULL,
                        actual_price REAL NOT NULL,
                        price_change REAL NOT NULL,
                        price_change_pct REAL NOT NULL,
                        is_correct BOOLEAN NOT NULL,
                        accuracy_score REAL NOT NULL,
                        market_condition_at_eval TEXT,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (signal_id) REFERENCES signals (id),
                        UNIQUE(signal_id, time_horizon_days)
                    )
                ''')
                
                # 创建市场条件表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS market_conditions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ticker TEXT NOT NULL,
                        date TEXT NOT NULL,
                        condition_type TEXT NOT NULL,
                        volatility REAL,
                        trend_direction TEXT,
                        trend_strength REAL,
                        price REAL,
                        volume REAL,
                        created_at TEXT NOT NULL,
                        UNIQUE(ticker, date)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_signals_date ON signals(signal_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_signals_agent ON signals(agent_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_accuracy_signal ON accuracy_evaluations(signal_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_market_date ON market_conditions(date)')
                
                conn.commit()
                print("✅ 数据库初始化完成")
                
            except Exception as e:
                print(f"❌ 数据库初始化失败: {e}")
                conn.rollback()
            finally:
                conn.close()
    
    def insert_signal(self, signal: SignalRecord) -> Optional[int]:
        """插入信号记录"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 设置创建时间
                if not signal.created_at:
                    signal.created_at = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO signals 
                    (agent_name, ticker, signal_date, signal_type, confidence, 
                     reasoning, current_price, market_condition, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    signal.agent_name, signal.ticker, signal.signal_date,
                    signal.signal_type, signal.confidence, signal.reasoning,
                    signal.current_price, signal.market_condition, signal.created_at
                ))
                
                signal_id = cursor.lastrowid
                conn.commit()
                return signal_id
                
            except Exception as e:
                print(f"❌ 插入信号记录失败: {e}")
                conn.rollback()
                return None
            finally:
                conn.close()
    
    def insert_accuracy_evaluation(self, accuracy: AccuracyRecord) -> Optional[int]:
        """插入准确性评估记录"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 设置创建时间
                if not accuracy.created_at:
                    accuracy.created_at = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO accuracy_evaluations 
                    (signal_id, evaluation_date, time_horizon_days, actual_price,
                     price_change, price_change_pct, is_correct, accuracy_score,
                     market_condition_at_eval, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    accuracy.signal_id, accuracy.evaluation_date, accuracy.time_horizon_days,
                    accuracy.actual_price, accuracy.price_change, accuracy.price_change_pct,
                    accuracy.is_correct, accuracy.accuracy_score, accuracy.market_condition_at_eval,
                    accuracy.created_at
                ))
                
                accuracy_id = cursor.lastrowid
                conn.commit()
                return accuracy_id
                
            except Exception as e:
                print(f"❌ 插入准确性评估失败: {e}")
                conn.rollback()
                return None
            finally:
                conn.close()
    
    def get_signals(self, 
                   agent_name: Optional[str] = None,
                   ticker: Optional[str] = None,
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   limit: Optional[int] = None) -> List[SignalRecord]:
        """查询信号记录"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                query = "SELECT * FROM signals WHERE 1=1"
                params = []
                
                if agent_name:
                    query += " AND agent_name = ?"
                    params.append(agent_name)
                
                if ticker:
                    query += " AND ticker = ?"
                    params.append(ticker)
                
                if start_date:
                    query += " AND signal_date >= ?"
                    params.append(start_date)
                
                if end_date:
                    query += " AND signal_date <= ?"
                    params.append(end_date)
                
                query += " ORDER BY signal_date DESC"
                
                if limit:
                    query += " LIMIT ?"
                    params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                # 转换为SignalRecord对象
                signals = []
                for row in rows:
                    signal = SignalRecord(
                        id=row[0], agent_name=row[1], ticker=row[2],
                        signal_date=row[3], signal_type=row[4], confidence=row[5],
                        reasoning=row[6], current_price=row[7], market_condition=row[8],
                        created_at=row[9]
                    )
                    signals.append(signal)
                
                return signals
                
            except Exception as e:
                print(f"❌ 查询信号记录失败: {e}")
                return []
            finally:
                conn.close()
    
    def get_accuracy_stats(self, 
                          agent_name: Optional[str] = None,
                          time_horizon_days: Optional[int] = None,
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None) -> Dict[str, Any]:
        """获取准确性统计"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                query = '''
                    SELECT 
                        s.agent_name,
                        a.time_horizon_days,
                        COUNT(*) as total_evaluations,
                        SUM(CASE WHEN a.is_correct THEN 1 ELSE 0 END) as correct_predictions,
                        AVG(CASE WHEN a.is_correct THEN 1.0 ELSE 0.0 END) as accuracy_rate,
                        AVG(a.accuracy_score) as avg_accuracy_score,
                        AVG(s.confidence) as avg_confidence
                    FROM signals s
                    JOIN accuracy_evaluations a ON s.id = a.signal_id
                    WHERE 1=1
                '''
                params = []
                
                if agent_name:
                    query += " AND s.agent_name = ?"
                    params.append(agent_name)
                
                if time_horizon_days:
                    query += " AND a.time_horizon_days = ?"
                    params.append(time_horizon_days)
                
                if start_date:
                    query += " AND s.signal_date >= ?"
                    params.append(start_date)
                
                if end_date:
                    query += " AND s.signal_date <= ?"
                    params.append(end_date)
                
                query += " GROUP BY s.agent_name, a.time_horizon_days"
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                stats = {}
                for row in rows:
                    agent = row[0]
                    horizon = row[1]
                    
                    if agent not in stats:
                        stats[agent] = {}
                    
                    stats[agent][f"{horizon}天"] = {
                        'total_evaluations': row[2],
                        'correct_predictions': row[3],
                        'accuracy_rate': row[4],
                        'avg_accuracy_score': row[5],
                        'avg_confidence': row[6]
                    }
                
                return stats
                
            except Exception as e:
                print(f"❌ 获取准确性统计失败: {e}")
                return {}
            finally:
                conn.close()
    
    def cleanup_old_records(self, retention_days: int = 365):
        """清理旧记录"""
        cutoff_date = (datetime.now() - timedelta(days=retention_days)).strftime('%Y-%m-%d')
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 删除旧的准确性评估记录
                cursor.execute('''
                    DELETE FROM accuracy_evaluations 
                    WHERE evaluation_date < ?
                ''', (cutoff_date,))
                
                # 删除没有关联准确性评估的旧信号记录
                cursor.execute('''
                    DELETE FROM signals 
                    WHERE signal_date < ? 
                    AND id NOT IN (SELECT DISTINCT signal_id FROM accuracy_evaluations)
                ''', (cutoff_date,))
                
                # 删除旧的市场条件记录
                cursor.execute('''
                    DELETE FROM market_conditions 
                    WHERE date < ?
                ''', (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                print(f"🧹 清理了 {deleted_count} 条旧记录")
                
            except Exception as e:
                print(f"❌ 清理旧记录失败: {e}")
                conn.rollback()
            finally:
                conn.close()
    
    def backup_database(self, backup_dir: str = "backups"):
        """备份数据库"""
        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = backup_path / f"signal_tracking_backup_{timestamp}.db"
        
        try:
            shutil.copy2(self.db_path, backup_file)
            print(f"✅ 数据库已备份到: {backup_file}")
            return str(backup_file)
        except Exception as e:
            print(f"❌ 数据库备份失败: {e}")
            return None
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 获取各表记录数
                cursor.execute("SELECT COUNT(*) FROM signals")
                signal_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM accuracy_evaluations")
                accuracy_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM market_conditions")
                market_count = cursor.fetchone()[0]
                
                # 获取日期范围
                cursor.execute("SELECT MIN(signal_date), MAX(signal_date) FROM signals")
                date_range = cursor.fetchone()
                
                # 获取代理数量
                cursor.execute("SELECT COUNT(DISTINCT agent_name) FROM signals")
                agent_count = cursor.fetchone()[0]
                
                return {
                    'signal_records': signal_count,
                    'accuracy_evaluations': accuracy_count,
                    'market_conditions': market_count,
                    'date_range': {
                        'start': date_range[0],
                        'end': date_range[1]
                    },
                    'unique_agents': agent_count,
                    'database_size_mb': os.path.getsize(self.db_path) / (1024 * 1024)
                }
                
            except Exception as e:
                print(f"❌ 获取数据库统计失败: {e}")
                return {}
            finally:
                conn.close()


if __name__ == "__main__":
    # 测试数据库功能
    db = SignalDatabase("test_signal_tracking.db")
    
    # 测试插入信号
    test_signal = SignalRecord(
        agent_name="warren_buffett_agent",
        ticker="AAPL",
        signal_date="2025-06-18",
        signal_type="bullish",
        confidence=85.0,
        reasoning="Strong fundamentals and good margin of safety",
        current_price=150.0,
        market_condition="bull"
    )
    
    signal_id = db.insert_signal(test_signal)
    print(f"插入信号ID: {signal_id}")
    
    # 测试查询信号
    signals = db.get_signals(limit=5)
    print(f"查询到 {len(signals)} 条信号记录")
    
    # 测试数据库统计
    stats = db.get_database_stats()
    print(f"数据库统计: {stats}")
