#!/usr/bin/env python3
"""
独立信号准确性跟踪系统 - 核心跟踪器类
"""

import os
import sys
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import schedule

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.analysis.signal_database import SignalDatabase
from src.analysis.accuracy_evaluator import AccuracyEvaluator
from src.analysis.market_condition_analyzer import MarketConditionAnalyzer
from src.analysis.signal_reporter import SignalReporter
from src.hooks.signal_interceptor import SignalInterceptor
from src.config.signal_tracker_config import get_config


class StandaloneSignalTracker:
    """独立信号准确性跟踪系统"""
    
    def __init__(self):
        self.config = get_config()
        
        # 初始化组件
        self.database = SignalDatabase(self.config.database_config.db_path)
        self.evaluator = AccuracyEvaluator(self.database)
        self.market_analyzer = MarketConditionAnalyzer()
        self.reporter = SignalReporter()
        self.interceptor = SignalInterceptor()
        
        # 运行状态
        self.is_running = False
        self.scheduler_thread = None
        self.lock = threading.Lock()
        
        print("🚀 独立信号跟踪系统已初始化")
    
    def start(self):
        """启动跟踪系统"""
        if self.is_running:
            print("⚠️  系统已在运行中")
            return
        
        with self.lock:
            self.is_running = True
            
            # 设置定时任务
            self._setup_scheduled_tasks()
            
            # 启动调度器线程
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            print("✅ 信号跟踪系统已启动")
            self._print_system_status()
    
    def stop(self):
        """停止跟踪系统"""
        with self.lock:
            self.is_running = False
            
            # 清除定时任务
            schedule.clear()
            
            print("🛑 信号跟踪系统已停止")
    
    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        # 每小时评估信号准确性
        schedule.every().hour.do(self._scheduled_accuracy_evaluation)
        
        # 每天备份数据库
        if self.config.database_config.backup_enabled:
            schedule.every(self.config.database_config.backup_interval_hours).hours.do(self._scheduled_backup)
        
        # 每天清理旧数据
        schedule.every().day.at("02:00").do(self._scheduled_cleanup)
        
        # 每30分钟生成报告（如果启用自动生成）
        if self.config.report_config.auto_generate:
            schedule.every(30).minutes.do(self._scheduled_report_generation)
        
        print("📅 定时任务已设置")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                print(f"❌ 调度器错误: {e}")
                time.sleep(60)
    
    def _scheduled_accuracy_evaluation(self):
        """定时准确性评估"""
        try:
            print("🔍 开始定时准确性评估...")
            
            current_date = datetime.now().strftime('%Y-%m-%d')
            enabled_agents = [agent.name for agent in self.config.get_enabled_agents()]
            
            total_evaluations = 0
            
            # 对每个启用的时间维度进行评估
            for horizon_config in self.config.get_enabled_time_horizons():
                evaluations = self.evaluator.batch_evaluate_signals(
                    current_date,
                    horizon_config.days,
                    enabled_agents
                )
                total_evaluations += len(evaluations)
                
                if evaluations:
                    print(f"  ✅ {horizon_config.name}: 评估了 {len(evaluations)} 个信号")
            
            print(f"📊 定时评估完成，总计评估 {total_evaluations} 个信号")
            
        except Exception as e:
            print(f"❌ 定时准确性评估失败: {e}")
    
    def _scheduled_backup(self):
        """定时数据库备份"""
        try:
            print("💾 开始数据库备份...")
            backup_file = self.database.backup_database()
            if backup_file:
                print(f"✅ 数据库备份完成: {backup_file}")
            else:
                print("❌ 数据库备份失败")
        except Exception as e:
            print(f"❌ 数据库备份错误: {e}")
    
    def _scheduled_cleanup(self):
        """定时清理旧数据"""
        try:
            print("🧹 开始清理旧数据...")
            self.database.cleanup_old_records(self.config.database_config.retention_days)
            print("✅ 数据清理完成")
        except Exception as e:
            print(f"❌ 数据清理错误: {e}")
    
    def _scheduled_report_generation(self):
        """定时生成报告"""
        try:
            print("📈 开始生成定时报告...")
            
            # 生成实时仪表板数据
            dashboard_data = self.reporter.generate_real_time_dashboard_data()
            
            # 导出为JSON格式
            if 'error' not in dashboard_data:
                report_file = self.reporter.export_report(dashboard_data, "json")
                print(f"✅ 实时报告已生成: {report_file}")
            else:
                print(f"⚠️  报告生成失败: {dashboard_data['error']}")
                
        except Exception as e:
            print(f"❌ 定时报告生成错误: {e}")
    
    def manual_accuracy_evaluation(self, 
                                 evaluation_date: Optional[str] = None,
                                 time_horizon_days: Optional[int] = None) -> Dict[str, Any]:
        """手动触发准确性评估"""
        if evaluation_date is None:
            evaluation_date = datetime.now().strftime('%Y-%m-%d')
        
        try:
            print(f"🔍 手动准确性评估: {evaluation_date}")
            
            enabled_agents = [agent.name for agent in self.config.get_enabled_agents()]
            results = {}
            
            # 如果指定了时间维度，只评估该维度
            if time_horizon_days:
                evaluations = self.evaluator.batch_evaluate_signals(
                    evaluation_date,
                    time_horizon_days,
                    enabled_agents
                )
                results[f"{time_horizon_days}天"] = {
                    'evaluations': len(evaluations),
                    'details': [eval.to_dict() for eval in evaluations]
                }
            else:
                # 评估所有启用的时间维度
                for horizon_config in self.config.get_enabled_time_horizons():
                    evaluations = self.evaluator.batch_evaluate_signals(
                        evaluation_date,
                        horizon_config.days,
                        enabled_agents
                    )
                    results[horizon_config.name] = {
                        'evaluations': len(evaluations),
                        'details': [eval.to_dict() for eval in evaluations]
                    }
            
            total_evaluations = sum(r['evaluations'] for r in results.values())
            print(f"✅ 手动评估完成，总计评估 {total_evaluations} 个信号")
            
            return {
                'success': True,
                'evaluation_date': evaluation_date,
                'total_evaluations': total_evaluations,
                'results_by_horizon': results
            }
            
        except Exception as e:
            print(f"❌ 手动准确性评估失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'evaluation_date': evaluation_date
            }
    
    def generate_comprehensive_report(self, 
                                    start_date: Optional[str] = None,
                                    end_date: Optional[str] = None,
                                    export_formats: Optional[List[str]] = None) -> Dict[str, Any]:
        """生成综合报告"""
        try:
            print("📊 生成综合报告...")
            
            # 生成报告数据
            report_data = self.reporter.generate_comprehensive_report(start_date, end_date)
            
            if 'error' in report_data:
                return report_data
            
            # 导出报告
            if export_formats is None:
                export_formats = self.config.report_config.export_formats
            
            exported_files = []
            for format_type in export_formats:
                try:
                    file_path = self.reporter.export_report(report_data, format_type)
                    exported_files.append(file_path)
                except Exception as e:
                    print(f"⚠️  导出 {format_type} 格式失败: {e}")
            
            print(f"✅ 综合报告生成完成，导出了 {len(exported_files)} 个文件")
            
            return {
                'success': True,
                'report_data': report_data,
                'exported_files': exported_files
            }
            
        except Exception as e:
            print(f"❌ 综合报告生成失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_real_time_dashboard(self) -> Dict[str, Any]:
        """获取实时仪表板数据"""
        try:
            return self.reporter.generate_real_time_dashboard_data()
        except Exception as e:
            print(f"❌ 获取仪表板数据失败: {e}")
            return {'error': str(e)}
    
    def get_agent_performance_summary(self, agent_name: Optional[str] = None) -> Dict[str, Any]:
        """获取代理表现摘要"""
        try:
            if agent_name:
                # 获取特定代理的表现
                performance = self.evaluator.calculate_agent_performance(agent_name)
                agent_config = self.config.get_agent_by_name(agent_name)
                if agent_config:
                    performance['display_name'] = agent_config.display_name
                return performance
            else:
                # 获取所有代理的比较表现
                performances = self.evaluator.get_comparative_performance(1)  # 1天时间维度
                return {
                    'comparative_performance': performances,
                    'total_agents': len(performances)
                }
        except Exception as e:
            print(f"❌ 获取代理表现失败: {e}")
            return {'error': str(e)}
    
    def _print_system_status(self):
        """打印系统状态"""
        print("\n" + "="*60)
        print("🎯 独立信号跟踪系统状态")
        print("="*60)
        
        # 系统配置
        print(f"📊 目标股票: {self.config.system_config['target_ticker']}")
        print(f"🔄 系统状态: {'运行中' if self.is_running else '已停止'}")
        
        # 代理状态
        enabled_agents = self.config.get_enabled_agents()
        print(f"👥 跟踪代理: {len(enabled_agents)}/{len(self.config.agents)}")
        
        # 时间维度
        enabled_horizons = self.config.get_enabled_time_horizons()
        print(f"⏰ 时间维度: {len(enabled_horizons)}/{len(self.config.time_horizons)}")
        
        # 数据库状态
        db_stats = self.database.get_database_stats()
        print(f"💾 信号记录: {db_stats.get('signal_records', 0)}")
        print(f"📈 准确性评估: {db_stats.get('accuracy_evaluations', 0)}")
        
        print("="*60)
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n🎯 独立信号跟踪系统")
            print("1. 查看系统状态")
            print("2. 手动准确性评估")
            print("3. 生成综合报告")
            print("4. 查看实时仪表板")
            print("5. 查看代理表现")
            print("6. 系统配置")
            print("7. 启动/停止系统")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-7): ").strip()
            
            if choice == "0":
                if self.is_running:
                    self.stop()
                print("👋 再见！")
                break
            elif choice == "1":
                self._print_system_status()
            elif choice == "2":
                self._interactive_accuracy_evaluation()
            elif choice == "3":
                self._interactive_report_generation()
            elif choice == "4":
                self._interactive_dashboard()
            elif choice == "5":
                self._interactive_agent_performance()
            elif choice == "6":
                self.config.print_config_summary()
            elif choice == "7":
                if self.is_running:
                    self.stop()
                else:
                    self.start()
            else:
                print("❌ 无效选择，请重试")
    
    def _interactive_accuracy_evaluation(self):
        """交互式准确性评估"""
        print("\n🔍 手动准确性评估")
        
        # 选择评估日期
        date_input = input("评估日期 (YYYY-MM-DD，回车使用今天): ").strip()
        evaluation_date = date_input if date_input else None
        
        # 选择时间维度
        print("\n可用时间维度:")
        horizons = self.config.get_enabled_time_horizons()
        for i, horizon in enumerate(horizons):
            print(f"{i+1}. {horizon.name} ({horizon.days}天)")
        print("0. 评估所有时间维度")
        
        horizon_choice = input("选择时间维度 (0-{}): ".format(len(horizons))).strip()
        
        time_horizon_days = None
        if horizon_choice.isdigit() and 1 <= int(horizon_choice) <= len(horizons):
            time_horizon_days = horizons[int(horizon_choice)-1].days
        
        # 执行评估
        result = self.manual_accuracy_evaluation(evaluation_date, time_horizon_days)
        
        if result['success']:
            print(f"✅ 评估完成！总计评估 {result['total_evaluations']} 个信号")
        else:
            print(f"❌ 评估失败: {result['error']}")
    
    def _interactive_report_generation(self):
        """交互式报告生成"""
        print("\n📊 生成综合报告")
        
        start_date = input("开始日期 (YYYY-MM-DD，回车使用30天前): ").strip() or None
        end_date = input("结束日期 (YYYY-MM-DD，回车使用今天): ").strip() or None
        
        result = self.generate_comprehensive_report(start_date, end_date)
        
        if result['success']:
            print(f"✅ 报告生成完成！导出了 {len(result['exported_files'])} 个文件")
            for file_path in result['exported_files']:
                print(f"  📄 {file_path}")
        else:
            print(f"❌ 报告生成失败: {result['error']}")
    
    def _interactive_dashboard(self):
        """交互式仪表板"""
        print("\n📈 实时仪表板")
        
        dashboard_data = self.get_real_time_dashboard()
        
        if 'error' not in dashboard_data:
            print(f"📊 系统状态: {dashboard_data['system_status']}")
            print(f"🕐 最近活动: {dashboard_data['recent_activity']}")
            print(f"🏆 代理排名: {len(dashboard_data.get('agent_rankings', []))} 个代理")
        else:
            print(f"❌ 获取仪表板数据失败: {dashboard_data['error']}")
    
    def _interactive_agent_performance(self):
        """交互式代理表现查看"""
        print("\n👥 代理表现分析")
        
        enabled_agents = self.config.get_enabled_agents()
        print("\n可用代理:")
        for i, agent in enumerate(enabled_agents):
            print(f"{i+1}. {agent.display_name}")
        print("0. 查看所有代理比较")
        
        choice = input(f"选择代理 (0-{len(enabled_agents)}): ").strip()
        
        if choice == "0":
            performance = self.get_agent_performance_summary()
            if 'error' not in performance:
                print(f"📊 比较了 {performance['total_agents']} 个代理的表现")
            else:
                print(f"❌ 获取表现数据失败: {performance['error']}")
        elif choice.isdigit() and 1 <= int(choice) <= len(enabled_agents):
            agent = enabled_agents[int(choice)-1]
            performance = self.get_agent_performance_summary(agent.name)
            if 'error' not in performance:
                print(f"📊 {agent.display_name} 表现:")
                print(f"  总信号数: {performance['total_signals']}")
                print(f"  整体准确率: {performance['overall_accuracy']:.1%}")
            else:
                print(f"❌ 获取表现数据失败: {performance['error']}")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    # 创建并启动独立信号跟踪系统
    tracker = StandaloneSignalTracker()
    
    try:
        # 启动系统
        tracker.start()
        
        # 进入交互式菜单
        tracker.interactive_menu()
        
    except KeyboardInterrupt:
        print("\n\n🛑 收到中断信号，正在停止系统...")
        tracker.stop()
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")
        tracker.stop()
