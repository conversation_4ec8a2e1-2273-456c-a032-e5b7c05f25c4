#!/usr/bin/env python3
"""
独立信号准确性跟踪系统 - 主入口脚本

这是一个独立运行的信号准确性跟踪系统，用于监控AI对冲基金代理的信号准确性。
系统可以独立于主要交易流程运行，提供实时的准确性分析和报告。

使用方法:
    python standalone_signal_tracker.py [选项]

选项:
    --config-file FILE    指定配置文件路径
    --interactive        启动交互式模式
    --daemon             后台运行模式
    --evaluate-date DATE 手动评估指定日期的信号
    --generate-report    生成综合报告
    --dashboard          显示实时仪表板
    --help               显示帮助信息

示例:
    # 交互式模式
    python standalone_signal_tracker.py --interactive
    
    # 后台运行
    python standalone_signal_tracker.py --daemon
    
    # 手动评估
    python standalone_signal_tracker.py --evaluate-date 2025-06-18
    
    # 生成报告
    python standalone_signal_tracker.py --generate-report
"""

import os
import sys
import argparse
import signal
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.analysis.standalone_signal_tracker import StandaloneSignalTracker
from src.config.signal_tracker_config import get_config


def signal_handler(signum, frame):
    """处理中断信号"""
    print(f"\n🛑 收到信号 {signum}，正在优雅关闭...")
    global tracker
    if tracker and tracker.is_running:
        tracker.stop()
    sys.exit(0)


def print_banner():
    """打印系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        🎯 AI对冲基金独立信号准确性跟踪系统 v1.0                ║
║                                                              ║
║  📊 实时监控16个AI代理的信号准确性                              ║
║  📈 多时间维度准确性评估 (1天/3天/1周/1月)                      ║
║  🏆 代理表现排名和比较分析                                      ║
║  📋 自动生成详细报告和仪表板                                    ║
║  🔄 独立运行，不干扰主交易流程                                  ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_help():
    """打印详细帮助信息"""
    help_text = """
🎯 AI对冲基金独立信号准确性跟踪系统

📋 功能特性:
  • 监控16个AI代理的信号准确性
  • 支持多时间维度评估 (1天、3天、1周、1月)
  • 实时仪表板和历史趋势分析
  • 市场条件相关性分析
  • 自动报告生成和导出
  • 独立运行，不影响主交易系统

🚀 使用方法:

  1. 交互式模式 (推荐新用户):
     python standalone_signal_tracker.py --interactive
     
  2. 后台运行模式:
     python standalone_signal_tracker.py --daemon
     
  3. 手动评估特定日期:
     python standalone_signal_tracker.py --evaluate-date 2025-06-18
     
  4. 生成综合报告:
     python standalone_signal_tracker.py --generate-report
     
  5. 查看实时仪表板:
     python standalone_signal_tracker.py --dashboard

⚙️  配置选项:
  • 配置文件位置: config/signal_tracker_config.json
  • 数据库位置: signal_tracking.db
  • 报告输出: signal_reports/
  
📊 监控的代理:
  投资风格代理: Warren Buffett, Ben Graham, Peter Lynch, Bill Ackman,
                Cathie Wood, Charlie Munger, Phil Fisher, Michael Burry,
                Stanley Druckenmiller, Aswath Damodaran
  
  分析代理: Technical Analyst, Fundamentals, Sentiment, Valuation,
           Factual News, Subjective News

🎯 评估维度:
  • 方向准确性: 预测方向 vs 实际价格变动
  • 幅度准确性: 预测变化幅度 vs 实际变化
  • 置信度分析: 高置信度信号的准确性
  • 市场条件: 不同市场环境下的表现

📈 报告格式:
  • JSON: 机器可读的详细数据
  • CSV: 表格数据，便于Excel分析
  • HTML: 可视化报告，便于查看

💡 提示:
  • 首次运行建议使用 --interactive 模式
  • 系统会自动使用本地Alpha Vantage数据避免API限制
  • 所有配置都可以通过配置文件或交互式界面修改
    """
    print(help_text)


def run_interactive_mode():
    """运行交互式模式"""
    print("🎮 启动交互式模式...")
    tracker = StandaloneSignalTracker()
    
    try:
        tracker.interactive_menu()
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在退出...")
    finally:
        if tracker.is_running:
            tracker.stop()


def run_daemon_mode():
    """运行后台模式"""
    print("🔄 启动后台运行模式...")
    tracker = StandaloneSignalTracker()
    
    try:
        tracker.start()
        print("✅ 系统已在后台启动")
        print("💡 按 Ctrl+C 停止系统")
        
        # 保持运行
        while tracker.is_running:
            time.sleep(60)
            
    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在停止...")
    finally:
        tracker.stop()


def run_manual_evaluation(evaluation_date: str):
    """运行手动评估"""
    print(f"🔍 手动评估日期: {evaluation_date}")
    tracker = StandaloneSignalTracker()
    
    try:
        # 验证日期格式
        datetime.strptime(evaluation_date, '%Y-%m-%d')
        
        result = tracker.manual_accuracy_evaluation(evaluation_date)
        
        if result['success']:
            print(f"✅ 评估完成！")
            print(f"📊 总计评估: {result['total_evaluations']} 个信号")
            
            for horizon, data in result['results_by_horizon'].items():
                print(f"  {horizon}: {data['evaluations']} 个评估")
        else:
            print(f"❌ 评估失败: {result['error']}")
            
    except ValueError:
        print("❌ 无效的日期格式，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        print(f"❌ 评估过程出错: {e}")


def run_report_generation():
    """运行报告生成"""
    print("📊 生成综合报告...")
    tracker = StandaloneSignalTracker()
    
    try:
        result = tracker.generate_comprehensive_report()
        
        if result['success']:
            print(f"✅ 报告生成完成！")
            print(f"📄 导出文件:")
            for file_path in result['exported_files']:
                print(f"  • {file_path}")
        else:
            print(f"❌ 报告生成失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 报告生成过程出错: {e}")


def run_dashboard():
    """显示实时仪表板"""
    print("📈 获取实时仪表板数据...")
    tracker = StandaloneSignalTracker()
    
    try:
        dashboard_data = tracker.get_real_time_dashboard()
        
        if 'error' not in dashboard_data:
            print("\n" + "="*60)
            print("📊 实时仪表板")
            print("="*60)
            
            # 系统状态
            system_status = dashboard_data.get('system_status', {})
            print(f"🎯 目标股票: {system_status.get('target_ticker', 'N/A')}")
            print(f"📊 总信号数: {system_status.get('total_signals', 0)}")
            print(f"📈 总评估数: {system_status.get('total_evaluations', 0)}")
            print(f"👥 跟踪代理: {system_status.get('tracked_agents', 0)}")
            
            # 最近活动
            recent_activity = dashboard_data.get('recent_activity', {})
            print(f"\n🕐 最近24小时:")
            print(f"  新信号: {recent_activity.get('signals_24h', 0)}")
            print(f"  活跃代理: {recent_activity.get('active_agents', 0)}")
            
            # 代理排名
            rankings = dashboard_data.get('agent_rankings', [])
            if rankings:
                print(f"\n🏆 代理准确性排名 (前5名):")
                for i, agent in enumerate(rankings[:5]):
                    print(f"  {i+1}. {agent['agent']}: {agent['accuracy']:.1%} ({agent['evaluations']} 评估)")
            
            # 最近信号
            recent_signals = recent_activity.get('recent_signals', [])
            if recent_signals:
                print(f"\n📡 最新信号:")
                for signal in recent_signals[-5:]:
                    print(f"  {signal['time']} | {signal['agent']} | {signal['signal']} ({signal['confidence']:.0f}%)")
            
            print("="*60)
            print(f"🕐 更新时间: {dashboard_data.get('last_updated', 'N/A')}")
            
        else:
            print(f"❌ 获取仪表板数据失败: {dashboard_data['error']}")
            
    except Exception as e:
        print(f"❌ 仪表板显示过程出错: {e}")


def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="AI对冲基金独立信号准确性跟踪系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument('--config-file', type=str, help='配置文件路径')
    parser.add_argument('--interactive', action='store_true', help='启动交互式模式')
    parser.add_argument('--daemon', action='store_true', help='后台运行模式')
    parser.add_argument('--evaluate-date', type=str, help='手动评估指定日期 (YYYY-MM-DD)')
    parser.add_argument('--generate-report', action='store_true', help='生成综合报告')
    parser.add_argument('--dashboard', action='store_true', help='显示实时仪表板')
    parser.add_argument('--help-detailed', action='store_true', help='显示详细帮助信息')
    
    args = parser.parse_args()
    
    # 显示横幅
    print_banner()
    
    # 显示详细帮助
    if args.help_detailed:
        print_help()
        return
    
    # 加载配置
    if args.config_file:
        print(f"📝 使用配置文件: {args.config_file}")
        # 这里可以添加自定义配置文件加载逻辑
    
    config = get_config()
    print(f"⚙️  配置已加载: {config.config_file}")
    
    # 全局tracker变量用于信号处理
    global tracker
    tracker = None
    
    try:
        # 根据参数执行相应操作
        if args.interactive:
            run_interactive_mode()
        elif args.daemon:
            run_daemon_mode()
        elif args.evaluate_date:
            run_manual_evaluation(args.evaluate_date)
        elif args.generate_report:
            run_report_generation()
        elif args.dashboard:
            run_dashboard()
        else:
            # 默认显示配置摘要并提示用户
            config.print_config_summary()
            print("\n💡 提示:")
            print("  使用 --interactive 启动交互式模式")
            print("  使用 --help-detailed 查看详细帮助")
            print("  使用 --dashboard 查看实时状态")
            
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
