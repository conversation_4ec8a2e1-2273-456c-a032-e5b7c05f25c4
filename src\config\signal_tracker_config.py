#!/usr/bin/env python3
"""
独立信号准确性跟踪系统配置管理
"""

import os
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class TimeHorizonConfig:
    """时间维度配置"""
    name: str
    days: int
    enabled: bool = True


@dataclass
class AgentConfig:
    """代理配置"""
    name: str
    display_name: str
    enabled: bool = True
    min_confidence_threshold: float = 0.0


@dataclass
class AccuracyConfig:
    """准确性评估配置"""
    price_change_threshold: float = 0.01  # 中性信号的价格变动阈值
    min_sample_size: int = 10  # 最小样本数量
    confidence_weight: bool = True  # 是否使用置信度加权
    deduplication_window_hours: int = 6  # 信号去重时间窗口


@dataclass
class MarketConditionConfig:
    """市场条件配置"""
    volatility_window: int = 20  # 波动率计算窗口
    trend_window: int = 50  # 趋势计算窗口
    bull_threshold: float = 0.05  # 牛市阈值
    bear_threshold: float = -0.05  # 熊市阈值


@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_path: str = "signal_tracking.db"
    backup_enabled: bool = True
    backup_interval_hours: int = 24
    retention_days: int = 365


@dataclass
class ReportConfig:
    """报告配置"""
    output_dir: str = "signal_reports"
    auto_generate: bool = True
    export_formats: List[str] = None
    dashboard_refresh_seconds: int = 30

    def __post_init__(self):
        if self.export_formats is None:
            self.export_formats = ["json", "csv", "html"]


class SignalTrackerConfig:
    """信号跟踪系统配置管理器"""
    
    def __init__(self, config_file: str = "signal_tracker_config.json"):
        self.config_file = config_file
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self._init_default_config()
        
        # 加载配置文件
        self.load_config()
    
    def _init_default_config(self):
        """初始化默认配置"""
        # 时间维度配置
        self.time_horizons = [
            TimeHorizonConfig("1天", 1),
            TimeHorizonConfig("3天", 3),
            TimeHorizonConfig("1周", 7),
            TimeHorizonConfig("1月", 30),
        ]
        
        # 代理配置 - 基于现有系统的16个代理
        self.agents = [
            # 投资风格代理
            AgentConfig("warren_buffett_agent", "Warren Buffett"),
            AgentConfig("ben_graham_agent", "Ben Graham"),
            AgentConfig("peter_lynch_agent", "Peter Lynch"),
            AgentConfig("bill_ackman_agent", "Bill Ackman"),
            AgentConfig("cathie_wood_agent", "Cathie Wood"),
            AgentConfig("charlie_munger_agent", "Charlie Munger"),
            AgentConfig("phil_fisher_agent", "Phil Fisher"),
            AgentConfig("michael_burry_agent", "Michael Burry"),
            AgentConfig("stanley_druckenmiller_agent", "Stanley Druckenmiller"),
            AgentConfig("aswath_damodaran_agent", "Aswath Damodaran"),
            
            # 分析代理
            AgentConfig("technical_analyst_agent", "Technical Analyst"),
            AgentConfig("fundamentals_agent", "Fundamentals"),
            AgentConfig("sentiment_agent", "Sentiment"),
            AgentConfig("valuation_agent", "Valuation"),
            AgentConfig("factual_news_agent", "Factual News"),
            AgentConfig("subjective_news_agent", "Subjective News"),
        ]
        
        # 其他配置
        self.accuracy_config = AccuracyConfig()
        self.market_condition_config = MarketConditionConfig()
        self.database_config = DatabaseConfig()
        self.report_config = ReportConfig()
        
        # 系统配置
        self.system_config = {
            "enabled": True,
            "target_ticker": "AAPL",
            "data_source": "local_alpha_vantage",  # 使用本地Alpha Vantage数据
            "bypass_portfolio_manager": True,
            "bypass_reflection_analyst": True,
            "log_level": "INFO",
            "max_concurrent_evaluations": 5,
        }
    
    def load_config(self):
        """从文件加载配置"""
        config_path = self.config_dir / self.config_file
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置
                self._update_from_dict(config_data)
                print(f"✅ 配置已从 {config_path} 加载")
                
            except Exception as e:
                print(f"⚠️  加载配置文件失败: {e}")
                print("使用默认配置")
        else:
            print(f"📝 配置文件不存在，将创建默认配置: {config_path}")
            self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        config_path = self.config_dir / self.config_file
        
        config_data = {
            "time_horizons": [asdict(th) for th in self.time_horizons],
            "agents": [asdict(agent) for agent in self.agents],
            "accuracy_config": asdict(self.accuracy_config),
            "market_condition_config": asdict(self.market_condition_config),
            "database_config": asdict(self.database_config),
            "report_config": asdict(self.report_config),
            "system_config": self.system_config,
        }
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置已保存到 {config_path}")
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        # 更新时间维度
        if "time_horizons" in config_data:
            self.time_horizons = [
                TimeHorizonConfig(**th) for th in config_data["time_horizons"]
            ]
        
        # 更新代理配置
        if "agents" in config_data:
            self.agents = [
                AgentConfig(**agent) for agent in config_data["agents"]
            ]
        
        # 更新其他配置
        if "accuracy_config" in config_data:
            self.accuracy_config = AccuracyConfig(**config_data["accuracy_config"])
        
        if "market_condition_config" in config_data:
            self.market_condition_config = MarketConditionConfig(**config_data["market_condition_config"])
        
        if "database_config" in config_data:
            self.database_config = DatabaseConfig(**config_data["database_config"])
        
        if "report_config" in config_data:
            self.report_config = ReportConfig(**config_data["report_config"])
        
        if "system_config" in config_data:
            self.system_config.update(config_data["system_config"])
    
    def get_enabled_agents(self) -> List[AgentConfig]:
        """获取启用的代理列表"""
        return [agent for agent in self.agents if agent.enabled]
    
    def get_enabled_time_horizons(self) -> List[TimeHorizonConfig]:
        """获取启用的时间维度列表"""
        return [th for th in self.time_horizons if th.enabled]
    
    def get_agent_by_name(self, name: str) -> Optional[AgentConfig]:
        """根据名称获取代理配置"""
        for agent in self.agents:
            if agent.name == name:
                return agent
        return None
    
    def enable_agent(self, name: str, enabled: bool = True):
        """启用/禁用代理"""
        agent = self.get_agent_by_name(name)
        if agent:
            agent.enabled = enabled
            self.save_config()
    
    def set_time_horizon_enabled(self, name: str, enabled: bool = True):
        """启用/禁用时间维度"""
        for th in self.time_horizons:
            if th.name == name:
                th.enabled = enabled
                self.save_config()
                break
    
    def update_system_config(self, **kwargs):
        """更新系统配置"""
        self.system_config.update(kwargs)
        self.save_config()

    def print_config_summary(self):
        """打印配置摘要"""
        print("\n📊 信号跟踪系统配置摘要")
        print("=" * 50)

        print(f"🎯 目标股票: {self.system_config['target_ticker']}")
        print(f"📊 数据源: {self.system_config['data_source']}")
        print(f"🔄 系统状态: {'启用' if self.system_config['enabled'] else '禁用'}")

        enabled_agents = self.get_enabled_agents()
        print(f"\n👥 启用的代理 ({len(enabled_agents)}/{len(self.agents)}):")
        for agent in enabled_agents:
            print(f"  ✅ {agent.display_name}")

        enabled_horizons = self.get_enabled_time_horizons()
        print(f"\n⏰ 启用的时间维度 ({len(enabled_horizons)}/{len(self.time_horizons)}):")
        for th in enabled_horizons:
            print(f"  ✅ {th.name} ({th.days}天)")

        print(f"\n🎯 准确性配置:")
        print(f"  价格变动阈值: {self.accuracy_config.price_change_threshold:.1%}")
        print(f"  最小样本数量: {self.accuracy_config.min_sample_size}")
        print(f"  置信度加权: {'是' if self.accuracy_config.confidence_weight else '否'}")

        print(f"\n💾 数据库配置:")
        print(f"  数据库路径: {self.database_config.db_path}")
        print(f"  数据保留天数: {self.database_config.retention_days}")

        print(f"\n📈 报告配置:")
        print(f"  输出目录: {self.report_config.output_dir}")
        print(f"  导出格式: {', '.join(self.report_config.export_formats)}")


# 全局配置实例
signal_tracker_config = SignalTrackerConfig()


def get_config() -> SignalTrackerConfig:
    """获取全局配置实例"""
    return signal_tracker_config


if __name__ == "__main__":
    # 测试配置系统
    config = SignalTrackerConfig()
    config.print_config_summary()
