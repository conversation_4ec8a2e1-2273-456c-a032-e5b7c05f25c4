#!/usr/bin/env python3
"""
信号报告生成器 - 生成准确性跟踪报告和实时仪表板
"""

import os
import sys
import json
import csv
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import statistics

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.analysis.signal_database import SignalDatabase
from src.analysis.accuracy_evaluator import AccuracyEvaluator
from src.analysis.market_condition_analyzer import MarketConditionAnalyzer
from src.config.signal_tracker_config import get_config


class SignalReporter:
    """信号报告生成器"""
    
    def __init__(self):
        self.config = get_config()
        self.database = SignalDatabase(self.config.database_config.db_path)
        self.evaluator = AccuracyEvaluator(self.database)
        self.market_analyzer = MarketConditionAnalyzer()
        
        # 创建报告输出目录
        self.output_dir = Path(self.config.report_config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_real_time_dashboard_data(self) -> Dict[str, Any]:
        """生成实时仪表板数据"""
        try:
            # 获取基本统计
            db_stats = self.database.get_database_stats()
            
            # 获取最近24小时的信号
            recent_signals = self._get_recent_signals(hours=24)
            
            # 获取代理表现排名
            agent_rankings = self._get_agent_rankings()
            
            # 获取市场条件摘要
            market_summary = self._get_market_condition_summary()
            
            # 获取准确性趋势
            accuracy_trends = self._get_accuracy_trends()
            
            dashboard_data = {
                'timestamp': datetime.now().isoformat(),
                'system_status': {
                    'enabled': self.config.system_config.get('enabled', True),
                    'target_ticker': self.config.system_config.get('target_ticker', 'AAPL'),
                    'total_signals': db_stats.get('signal_records', 0),
                    'total_evaluations': db_stats.get('accuracy_evaluations', 0),
                    'tracked_agents': db_stats.get('unique_agents', 0)
                },
                'recent_activity': {
                    'signals_24h': len(recent_signals),
                    'recent_signals': [self._format_signal_for_dashboard(s) for s in recent_signals[-10:]],
                    'active_agents': len(set(s.agent_name for s in recent_signals))
                },
                'agent_rankings': agent_rankings,
                'market_summary': market_summary,
                'accuracy_trends': accuracy_trends,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return dashboard_data
            
        except Exception as e:
            print(f"❌ 生成仪表板数据失败: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def _get_recent_signals(self, hours: int = 24) -> List:
        """获取最近的信号"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d')
        
        signals = self.database.get_signals(
            ticker=self.config.system_config['target_ticker'],
            start_date=start_date,
            end_date=end_date,
            limit=100
        )
        
        # 过滤最近几小时的信号
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_signals = []
        
        for signal in signals:
            signal_time = datetime.fromisoformat(signal.created_at)
            if signal_time >= cutoff_time:
                recent_signals.append(signal)
        
        return recent_signals
    
    def _format_signal_for_dashboard(self, signal) -> Dict[str, Any]:
        """格式化信号用于仪表板显示"""
        agent_config = self.config.get_agent_by_name(signal.agent_name)
        display_name = agent_config.display_name if agent_config else signal.agent_name
        
        return {
            'agent': display_name,
            'signal': signal.signal_type.upper(),
            'confidence': signal.confidence,
            'time': signal.created_at[:16],  # YYYY-MM-DD HH:MM
            'price': signal.current_price,
            'market_condition': signal.market_condition
        }
    
    def _get_agent_rankings(self) -> List[Dict[str, Any]]:
        """获取代理准确性排名"""
        try:
            # 获取1天时间维度的比较表现
            performances = self.evaluator.get_comparative_performance(
                time_horizon_days=1,
                start_date=(datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
                end_date=datetime.now().strftime('%Y-%m-%d')
            )
            
            rankings = []
            for i, perf in enumerate(performances[:10]):  # 前10名
                agent_config = self.config.get_agent_by_name(perf['agent_name'])
                display_name = agent_config.display_name if agent_config else perf['agent_name']
                
                rankings.append({
                    'rank': i + 1,
                    'agent': display_name,
                    'accuracy': perf['selected_horizon_accuracy'],
                    'total_signals': perf['total_signals'],
                    'evaluations': perf['selected_horizon_evaluations'],
                    'overall_accuracy': perf['overall_accuracy']
                })
            
            return rankings
            
        except Exception as e:
            print(f"⚠️  获取代理排名失败: {e}")
            return []
    
    def _get_market_condition_summary(self) -> Dict[str, Any]:
        """获取市场条件摘要"""
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            summary = self.market_analyzer.get_market_condition_summary(
                self.config.system_config['target_ticker'],
                start_date,
                end_date
            )
            
            return summary
            
        except Exception as e:
            print(f"⚠️  获取市场条件摘要失败: {e}")
            return {}
    
    def _get_accuracy_trends(self) -> Dict[str, Any]:
        """获取准确性趋势数据"""
        try:
            # 获取过去30天的准确性统计
            stats = self.database.get_accuracy_stats(
                start_date=(datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
                end_date=datetime.now().strftime('%Y-%m-%d')
            )
            
            # 计算整体趋势
            total_evaluations = 0
            total_correct = 0
            
            for agent_stats in stats.values():
                for horizon_stats in agent_stats.values():
                    total_evaluations += horizon_stats['total_evaluations']
                    total_correct += horizon_stats['correct_predictions']
            
            overall_accuracy = total_correct / total_evaluations if total_evaluations > 0 else 0
            
            return {
                'overall_accuracy': overall_accuracy,
                'total_evaluations': total_evaluations,
                'period_days': 30
            }
            
        except Exception as e:
            print(f"⚠️  获取准确性趋势失败: {e}")
            return {}
    
    def generate_comprehensive_report(self, 
                                    start_date: Optional[str] = None,
                                    end_date: Optional[str] = None) -> Dict[str, Any]:
        """生成综合报告"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        try:
            # 获取所有代理的详细表现
            agent_performances = []
            enabled_agents = self.config.get_enabled_agents()

            for agent_config in enabled_agents:
                performance = self.evaluator.calculate_agent_performance(
                    agent_config.name, start_date, end_date
                )
                performance['display_name'] = agent_config.display_name
                # 确保必要字段存在
                if 'total_evaluations' not in performance:
                    performance['total_evaluations'] = 0
                agent_performances.append(performance)
            
            # 按整体准确率排序
            agent_performances.sort(key=lambda x: x['overall_accuracy'], reverse=True)
            
            # 获取市场条件分析
            market_analysis = self.market_analyzer.get_market_condition_summary(
                self.config.system_config['target_ticker'],
                start_date,
                end_date
            )
            
            # 获取数据库统计
            db_stats = self.database.get_database_stats()
            
            report = {
                'report_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'period': f"{start_date} to {end_date}",
                    'target_ticker': self.config.system_config['target_ticker'],
                    'report_type': 'comprehensive'
                },
                'summary': {
                    'total_agents': len(agent_performances),
                    'total_signals': sum(p['total_signals'] for p in agent_performances),
                    'total_evaluations': sum(p['total_evaluations'] for p in agent_performances),
                    'overall_accuracy': statistics.mean([p['overall_accuracy'] for p in agent_performances if p['overall_accuracy'] > 0]) if [p for p in agent_performances if p['overall_accuracy'] > 0] else 0,
                    'database_stats': db_stats
                },
                'agent_performances': agent_performances,
                'market_analysis': market_analysis,
                'time_horizon_analysis': self._analyze_time_horizons(start_date, end_date),
                'recommendations': self._generate_recommendations(agent_performances)
            }
            
            return report
            
        except Exception as e:
            print(f"❌ 生成综合报告失败: {e}")
            return {'error': str(e)}
    
    def _analyze_time_horizons(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """分析不同时间维度的表现"""
        time_horizon_analysis = {}
        
        for horizon_config in self.config.get_enabled_time_horizons():
            performances = self.evaluator.get_comparative_performance(
                horizon_config.days, start_date, end_date
            )
            
            if performances:
                valid_performances = [p for p in performances if p['selected_horizon_evaluations'] > 0]
                avg_accuracy = statistics.mean([p['selected_horizon_accuracy'] for p in valid_performances]) if valid_performances else 0
                total_evaluations = sum(p['selected_horizon_evaluations'] for p in performances)

                time_horizon_analysis[horizon_config.name] = {
                    'days': horizon_config.days,
                    'average_accuracy': avg_accuracy,
                    'total_evaluations': total_evaluations,
                    'top_performers': performances[:3]  # 前3名
                }
        
        return time_horizon_analysis
    
    def _generate_recommendations(self, agent_performances: List[Dict[str, Any]]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if not agent_performances:
            recommendations.append("需要更多信号数据来生成有意义的建议")
            return recommendations
        
        # 分析表现最好和最差的代理
        best_performer = agent_performances[0]
        worst_performer = agent_performances[-1]
        
        if best_performer['overall_accuracy'] > 0.7:
            recommendations.append(f"🏆 {best_performer['display_name']} 表现优异 (准确率: {best_performer['overall_accuracy']:.1%})，建议增加其信号权重")
        
        if worst_performer['overall_accuracy'] < 0.4 and worst_performer['total_evaluations'] > 10:
            recommendations.append(f"⚠️  {worst_performer['display_name']} 表现较差 (准确率: {worst_performer['overall_accuracy']:.1%})，建议检查其分析逻辑")
        
        # 分析置信度与准确性的关系
        high_confidence_agents = [p for p in agent_performances if p.get('confidence_analysis', {}).get('mean', 0) > 70]
        if high_confidence_agents:
            avg_accuracy = statistics.mean([p['overall_accuracy'] for p in high_confidence_agents])
            if avg_accuracy > 0.6:
                recommendations.append("💡 高置信度信号通常更准确，建议优先考虑置信度>70%的信号")
        
        # 检查样本数量
        low_sample_agents = [p for p in agent_performances if p['total_evaluations'] < self.config.accuracy_config.min_sample_size]
        if low_sample_agents:
            recommendations.append(f"📊 {len(low_sample_agents)} 个代理的评估样本不足，建议收集更多数据")
        
        return recommendations
    
    def export_report(self, report_data: Dict[str, Any], format_type: str = "json") -> str:
        """导出报告到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if format_type == "json":
            filename = f"signal_report_{timestamp}.json"
            filepath = self.output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        elif format_type == "csv":
            filename = f"signal_report_{timestamp}.csv"
            filepath = self.output_dir / filename
            
            # 导出代理表现数据
            if 'agent_performances' in report_data:
                with open(filepath, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['Agent', 'Overall_Accuracy', 'Total_Signals', 'Total_Evaluations'])
                    
                    for perf in report_data['agent_performances']:
                        writer.writerow([
                            perf.get('display_name', perf['agent_name']),
                            perf['overall_accuracy'],
                            perf['total_signals'],
                            perf['total_evaluations']
                        ])
        
        elif format_type == "html":
            filename = f"signal_report_{timestamp}.html"
            filepath = self.output_dir / filename
            
            html_content = self._generate_html_report(report_data)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
        
        print(f"✅ 报告已导出: {filepath}")
        return str(filepath)
    
    def _generate_html_report(self, report_data: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>AI对冲基金信号准确性报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .summary {{ margin: 20px 0; }}
                .agent-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .agent-table th, .agent-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .agent-table th {{ background-color: #f2f2f2; }}
                .recommendation {{ background-color: #e7f3ff; padding: 10px; margin: 5px 0; border-radius: 3px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>AI对冲基金信号准确性报告</h1>
                <p>生成时间: {report_data.get('report_metadata', {}).get('generated_at', '')}</p>
                <p>分析期间: {report_data.get('report_metadata', {}).get('period', '')}</p>
                <p>目标股票: {report_data.get('report_metadata', {}).get('target_ticker', '')}</p>
            </div>
            
            <div class="summary">
                <h2>摘要统计</h2>
                <p>总代理数: {report_data.get('summary', {}).get('total_agents', 0)}</p>
                <p>总信号数: {report_data.get('summary', {}).get('total_signals', 0)}</p>
                <p>总评估数: {report_data.get('summary', {}).get('total_evaluations', 0)}</p>
                <p>整体准确率: {report_data.get('summary', {}).get('overall_accuracy', 0):.1%}</p>
            </div>
            
            <h2>代理表现排名</h2>
            <table class="agent-table">
                <tr>
                    <th>排名</th>
                    <th>代理</th>
                    <th>准确率</th>
                    <th>信号数</th>
                    <th>评估数</th>
                </tr>
        """
        
        # 添加代理表现数据
        for i, perf in enumerate(report_data.get('agent_performances', [])[:10]):
            html += f"""
                <tr>
                    <td>{i+1}</td>
                    <td>{perf.get('display_name', perf['agent_name'])}</td>
                    <td>{perf['overall_accuracy']:.1%}</td>
                    <td>{perf['total_signals']}</td>
                    <td>{perf['total_evaluations']}</td>
                </tr>
            """
        
        html += """
            </table>
            
            <h2>改进建议</h2>
        """
        
        # 添加建议
        for rec in report_data.get('recommendations', []):
            html += f'<div class="recommendation">{rec}</div>'
        
        html += """
        </body>
        </html>
        """
        
        return html


if __name__ == "__main__":
    # 测试报告生成器
    reporter = SignalReporter()
    
    # 生成实时仪表板数据
    dashboard_data = reporter.generate_real_time_dashboard_data()
    print("仪表板数据生成完成")
    
    # 生成综合报告
    comprehensive_report = reporter.generate_comprehensive_report()
    print("综合报告生成完成")
    
    # 导出报告
    json_file = reporter.export_report(comprehensive_report, "json")
    html_file = reporter.export_report(comprehensive_report, "html")
    
    print(f"报告已导出: {json_file}, {html_file}")
