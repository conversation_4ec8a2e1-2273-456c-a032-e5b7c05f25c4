#!/usr/bin/env python3
"""
信号拦截器 - 与现有AI对冲基金系统集成，拦截代理信号进行跟踪
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import threading

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.analysis.signal_database import SignalDatabase, SignalRecord
from src.analysis.market_condition_analyzer import MarketConditionAnalyzer
from src.config.signal_tracker_config import get_config
from src.tools.api import get_current_price


class SignalInterceptor:
    """信号拦截器 - 拦截和跟踪代理信号"""

    def __init__(self):
        self.config = get_config()
        self.database = SignalDatabase(self.config.database_config.db_path)
        self.market_analyzer = MarketConditionAnalyzer()
        self.lock = threading.Lock()

        # 信号去重缓存
        self._signal_cache = {}
        self._cache_lock = threading.Lock()

        print("🎯 信号拦截器已初始化")

    def intercept_agent_signal(self,
                             agent_name: str,
                             ticker: str,
                             signal_data: Dict[str, Any],
                             signal_date: Optional[str] = None) -> bool:
        """
        拦截代理信号

        Args:
            agent_name: 代理名称
            ticker: 股票代码
            signal_data: 信号数据
            signal_date: 信号日期，默认为当前日期

        Returns:
            bool: 是否成功拦截和存储
        """
        if not self.config.system_config.get('enabled', True):
            return False

        # 检查是否是目标股票
        target_ticker = self.config.system_config.get('target_ticker', 'AAPL')
        if ticker != target_ticker:
            return False

        # 检查代理是否在跟踪列表中
        agent_config = self.config.get_agent_by_name(agent_name)
        if not agent_config or not agent_config.enabled:
            return False

        try:
            # 提取信号信息
            signal_type = signal_data.get('signal', '').lower()
            confidence = float(signal_data.get('confidence', 0.0))
            reasoning = signal_data.get('reasoning', '')

            # 验证信号类型
            if signal_type not in ['bullish', 'bearish', 'neutral']:
                print(f"⚠️  无效信号类型: {signal_type}")
                return False

            # 检查置信度阈值
            if confidence < agent_config.min_confidence_threshold:
                print(f"⚠️  置信度过低: {confidence} < {agent_config.min_confidence_threshold}")
                return False

            # 设置信号日期
            if signal_date is None:
                signal_date = datetime.now().strftime('%Y-%m-%d')

            # 检查信号去重
            if self._is_duplicate_signal(agent_name, ticker, signal_date, signal_type):
                print(f"🔄 重复信号已跳过: {agent_name} - {ticker} - {signal_date}")
                return False

            # 获取当前价格
            current_price = 0.0
            try:
                price_result = get_current_price(ticker, signal_date)
                current_price = float(price_result) if price_result is not None else 0.0
            except Exception as e:
                print(f"⚠️  获取价格失败: {e}")
                current_price = 0.0

            # 分析市场条件
            market_condition = ""
            try:
                market_analysis = self.market_analyzer.analyze_market_condition(ticker, signal_date)
                market_condition = market_analysis.get('condition_type', '')
            except Exception as e:
                print(f"⚠️  市场条件分析失败: {e}")

            # 创建信号记录
            signal_record = SignalRecord(
                agent_name=agent_name,
                ticker=ticker,
                signal_date=signal_date,
                signal_type=signal_type,
                confidence=confidence,
                reasoning=str(reasoning),
                current_price=current_price,
                market_condition=market_condition,
                created_at=datetime.now().isoformat()
            )

            # 存储信号
            signal_id = self.database.insert_signal(signal_record)

            if signal_id:
                # 更新去重缓存
                self._update_signal_cache(agent_name, ticker, signal_date, signal_type)

                print(f"✅ 信号已拦截: {agent_config.display_name} - {signal_type.upper()} - {confidence:.1f}%")
                return True
            else:
                print(f"❌ 信号存储失败")
                return False

        except Exception as e:
            print(f"❌ 信号拦截失败: {e}")
            return False

    def _is_duplicate_signal(self,
                           agent_name: str,
                           ticker: str,
                           signal_date: str,
                           signal_type: str) -> bool:
        """检查是否为重复信号"""
        with self._cache_lock:
            cache_key = f"{agent_name}_{ticker}_{signal_date}"

            # 检查缓存
            if cache_key in self._signal_cache:
                cached_signal = self._signal_cache[cache_key]

                # 检查时间窗口
                cached_time = datetime.fromisoformat(cached_signal['timestamp'])
                current_time = datetime.now()

                time_diff = (current_time - cached_time).total_seconds() / 3600  # 小时
                window_hours = self.config.accuracy_config.deduplication_window_hours

                if time_diff < window_hours and cached_signal['signal_type'] == signal_type:
                    return True

            return False

    def _update_signal_cache(self,
                           agent_name: str,
                           ticker: str,
                           signal_date: str,
                           signal_type: str):
        """更新信号缓存"""
        with self._cache_lock:
            cache_key = f"{agent_name}_{ticker}_{signal_date}"
            self._signal_cache[cache_key] = {
                'signal_type': signal_type,
                'timestamp': datetime.now().isoformat()
            }

            # 清理过期缓存
            self._cleanup_signal_cache()

    def _cleanup_signal_cache(self):
        """清理过期的信号缓存"""
        current_time = datetime.now()
        window_hours = self.config.accuracy_config.deduplication_window_hours

        expired_keys = []
        for key, value in self._signal_cache.items():
            cached_time = datetime.fromisoformat(value['timestamp'])
            time_diff = (current_time - cached_time).total_seconds() / 3600

            if time_diff > window_hours * 2:  # 保留2倍窗口时间
                expired_keys.append(key)

        for key in expired_keys:
            del self._signal_cache[key]

    def intercept_portfolio_manager_signals(self, signals_by_ticker: Dict[str, Dict[str, Any]]) -> bool:
        """
        拦截投资组合管理器处理前的所有代理信号

        Args:
            signals_by_ticker: 按股票分组的信号字典

        Returns:
            bool: 是否成功拦截
        """
        success_count = 0
        total_count = 0

        for ticker, agent_signals in signals_by_ticker.items():
            for agent_name, signal_data in agent_signals.items():
                total_count += 1

                if self.intercept_agent_signal(agent_name, ticker, signal_data):
                    success_count += 1

        if total_count > 0:
            success_rate = success_count / total_count * 100
            print(f"📊 信号拦截完成: {success_count}/{total_count} ({success_rate:.1f}%)")

        return success_count > 0


# 全局拦截器实例
_signal_interceptor = None
_interceptor_lock = threading.Lock()


def get_signal_interceptor() -> SignalInterceptor:
    """获取全局信号拦截器实例"""
    global _signal_interceptor

    with _interceptor_lock:
        if _signal_interceptor is None:
            _signal_interceptor = SignalInterceptor()
        return _signal_interceptor


# 便捷函数
def track_signal(agent_name: str, ticker: str, signal_data: Dict[str, Any]) -> bool:
    """便捷函数：跟踪单个信号"""
    interceptor = get_signal_interceptor()
    return interceptor.intercept_agent_signal(agent_name, ticker, signal_data)


def track_portfolio_signals(signals_by_ticker: Dict[str, Dict[str, Any]]) -> bool:
    """便捷函数：跟踪投资组合信号"""
    interceptor = get_signal_interceptor()
    return interceptor.intercept_portfolio_manager_signals(signals_by_ticker)


if __name__ == "__main__":
    # 测试信号拦截器
    interceptor = SignalInterceptor()

    # 测试信号拦截
    test_signal = {
        'signal': 'bullish',
        'confidence': 85.0,
        'reasoning': 'Strong fundamentals and good technical indicators'
    }

    success = interceptor.intercept_agent_signal(
        'warren_buffett_agent',
        'AAPL',
        test_signal
    )

    print(f"测试信号拦截: {'成功' if success else '失败'}")