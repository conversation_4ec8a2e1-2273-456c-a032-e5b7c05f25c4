# AI对冲基金独立信号准确性跟踪系统

## 🎯 系统概述

这是一个独立运行的信号准确性跟踪系统，专门用于监控AI对冲基金系统中16个代理的信号准确性。系统可以独立于主要交易流程运行，提供实时的准确性分析、历史趋势和详细报告。

## ✨ 主要特性

### 📊 代理覆盖
- **投资风格代理**: <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Aswath Damodaran
- **分析代理**: Technical Analyst, Fundamentals, Sentiment, Valuation, Factual News, Subjective News

### ⏰ 多时间维度评估
- 1天准确性评估
- 3天准确性评估  
- 1周准确性评估
- 1月准确性评估

### 🎯 准确性评估框架
- **方向准确性**: 预测方向 vs 实际价格变动
- **幅度准确性**: 预测变化幅度 vs 实际变化
- **置信度分析**: 高置信度信号的准确性表现
- **市场条件相关性**: 不同市场环境下的表现分析

### 📈 技术实现
- **数据源**: 使用本地Alpha Vantage数据避免API速率限制
- **信号拦截**: 在投资组合管理器处理前拦截原始信号
- **去重机制**: 6小时窗口内的重复信号自动去重
- **独立运行**: 不干扰现有交易流程

## 🚀 快速开始

### 1. 安装依赖

```bash
# 确保已安装项目依赖
pip install -r requirements.txt

# 额外依赖（如果需要）
pip install schedule sqlite3
```

### 2. 配置系统

```bash
# 查看当前配置
python standalone_signal_tracker.py --dashboard

# 交互式配置
python standalone_signal_tracker.py --interactive
```

### 3. 启动系统

#### 交互式模式（推荐新用户）
```bash
python standalone_signal_tracker.py --interactive
```

#### 后台运行模式
```bash
python standalone_signal_tracker.py --daemon
```

#### 查看实时状态
```bash
python standalone_signal_tracker.py --dashboard
```

## 📋 使用指南

### 基本操作

#### 1. 手动评估信号准确性
```bash
# 评估特定日期
python standalone_signal_tracker.py --evaluate-date 2025-06-18

# 交互式评估
python standalone_signal_tracker.py --interactive
# 然后选择 "2. 手动准确性评估"
```

#### 2. 生成综合报告
```bash
# 生成默认报告（过去30天）
python standalone_signal_tracker.py --generate-report

# 交互式报告生成
python standalone_signal_tracker.py --interactive
# 然后选择 "3. 生成综合报告"
```

#### 3. 查看代理表现
```bash
# 通过交互式界面
python standalone_signal_tracker.py --interactive
# 然后选择 "5. 查看代理表现"
```

### 高级功能

#### 1. 系统集成
```bash
# 自动集成到现有AI对冲基金系统
python src/hooks/portfolio_manager_integration.py --auto-integrate

# 测试集成状态
python src/hooks/portfolio_manager_integration.py --test

# 查看集成状态
python src/hooks/portfolio_manager_integration.py --status
```

#### 2. 配置管理
```bash
# 编辑配置文件
# config/signal_tracker_config.json

# 或通过交互式界面
python standalone_signal_tracker.py --interactive
# 然后选择 "6. 系统配置"
```

## 📁 文件结构

```
ai-hedge-fund/
├── standalone_signal_tracker.py          # 主入口脚本
├── SIGNAL_TRACKER_README.md             # 本文档
├── config/
│   └── signal_tracker_config.json       # 系统配置文件
├── src/
│   ├── analysis/
│   │   ├── standalone_signal_tracker.py # 核心跟踪器
│   │   ├── signal_database.py          # 数据库管理
│   │   ├── accuracy_evaluator.py       # 准确性评估器
│   │   ├── market_condition_analyzer.py # 市场条件分析
│   │   └── signal_reporter.py          # 报告生成器
│   ├── hooks/
│   │   ├── signal_interceptor.py       # 信号拦截器
│   │   └── portfolio_manager_integration.py # 系统集成
│   └── config/
│       └── signal_tracker_config.py    # 配置管理
├── signal_reports/                      # 报告输出目录
├── signal_tracking.db                   # SQLite数据库
└── backups/                            # 数据库备份目录
```

## ⚙️ 配置选项

### 主要配置项

```json
{
  "system_config": {
    "enabled": true,
    "target_ticker": "AAPL",
    "data_source": "local_alpha_vantage",
    "bypass_portfolio_manager": true,
    "bypass_reflection_analyst": true
  },
  "accuracy_config": {
    "price_change_threshold": 0.01,
    "min_sample_size": 10,
    "confidence_weight": true,
    "deduplication_window_hours": 6
  },
  "time_horizons": [
    {"name": "1天", "days": 1, "enabled": true},
    {"name": "3天", "days": 3, "enabled": true},
    {"name": "1周", "days": 7, "enabled": true},
    {"name": "1月", "days": 30, "enabled": true}
  ]
}
```

### 代理配置

所有16个代理默认启用，可以通过配置文件或交互式界面单独启用/禁用：

```json
{
  "agents": [
    {"name": "warren_buffett_agent", "display_name": "Warren Buffett", "enabled": true},
    {"name": "ben_graham_agent", "display_name": "Ben Graham", "enabled": true},
    // ... 其他代理
  ]
}
```

## 📊 报告格式

### 1. JSON报告
- 机器可读的详细数据
- 包含所有统计信息和原始数据
- 适合进一步分析和处理

### 2. CSV报告
- 表格格式数据
- 便于Excel分析
- 包含代理表现排名

### 3. HTML报告
- 可视化报告
- 包含图表和统计摘要
- 便于查看和分享

## 🔍 准确性评估方法

### 信号类型评估

1. **看涨信号 (Bullish)**
   - 正确：实际价格上涨 (> 0%)
   - 错误：实际价格下跌或持平

2. **看跌信号 (Bearish)**
   - 正确：实际价格下跌 (< 0%)
   - 错误：实际价格上涨或持平

3. **中性信号 (Neutral)**
   - 正确：价格变动在阈值内 (默认±1%)
   - 错误：价格变动超出阈值

### 准确性分数计算

```python
# 基础分数
base_score = 100 if correct else 0

# 置信度加权（如果启用）
if confidence_weight_enabled:
    base_score *= (confidence / 100)

# 幅度奖励（方向性信号）
if signal_type in ["bullish", "bearish"]:
    magnitude_bonus = min(abs(price_change) * 100, 50)
    base_score += magnitude_bonus

# 最终分数
final_score = min(base_score, 100)
```

## 🛠️ 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库文件权限
ls -la signal_tracking.db

# 重新初始化数据库
rm signal_tracking.db
python standalone_signal_tracker.py --interactive
```

#### 2. 价格数据获取失败
```bash
# 检查本地Alpha Vantage数据
ls -la AAPL_alpha_news/

# 确保新闻配置正确
python -c "from src.config.news_config import print_news_config_status; print_news_config_status()"
```

#### 3. 信号拦截不工作
```bash
# 测试集成状态
python src/hooks/portfolio_manager_integration.py --test

# 重新集成
python src/hooks/portfolio_manager_integration.py --auto-integrate
```

### 日志和调试

系统会在控制台输出详细的运行日志：
- ✅ 成功操作
- ⚠️ 警告信息  
- ❌ 错误信息
- 📊 统计信息

## 🔄 系统维护

### 定时任务

系统自动执行以下定时任务：
- **每小时**: 评估信号准确性
- **每天**: 数据库备份和清理
- **每30分钟**: 生成实时报告（如果启用）

### 数据备份

```bash
# 手动备份
python -c "
from src.analysis.signal_database import SignalDatabase
db = SignalDatabase()
db.backup_database()
"

# 查看备份文件
ls -la backups/
```

### 数据清理

```bash
# 手动清理旧数据（保留365天）
python -c "
from src.analysis.signal_database import SignalDatabase
db = SignalDatabase()
db.cleanup_old_records(365)
"
```

## 📞 支持和贡献

### 获取帮助
```bash
# 查看详细帮助
python standalone_signal_tracker.py --help-detailed

# 查看系统状态
python standalone_signal_tracker.py --dashboard
```

### 系统要求
- Python 3.8+
- SQLite3
- 现有AI对冲基金系统
- 本地Alpha Vantage数据

### 性能优化
- 数据库索引自动创建
- 信号去重减少重复处理
- 多线程支持并发评估
- 内存缓存提高查询速度

---

🎯 **开始使用**: `python standalone_signal_tracker.py --interactive`

📊 **查看状态**: `python standalone_signal_tracker.py --dashboard`

🔧 **系统集成**: `python src/hooks/portfolio_manager_integration.py --auto-integrate`
