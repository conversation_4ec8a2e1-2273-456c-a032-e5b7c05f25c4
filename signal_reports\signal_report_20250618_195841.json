{"report_metadata": {"generated_at": "2025-06-18T19:58:41.913025", "period": "2025-05-19 to 2025-06-18", "target_ticker": "AAPL", "report_type": "comprehensive"}, "summary": {"total_agents": 16, "total_signals": 1, "total_evaluations": 1, "overall_accuracy": 1.0, "database_stats": {"signal_records": 4, "accuracy_evaluations": 1, "market_conditions": 0, "date_range": {"start": "2025-06-15", "end": "2025-06-18"}, "unique_agents": 3, "database_size_mb": 0.046875}}, "agent_performances": [{"agent_name": "technical_analyst_agent", "total_signals": 1, "total_evaluations": 1, "overall_accuracy": 1.0, "time_horizon_performance": {"1天": {"total_evaluations": 1, "correct_predictions": 1, "accuracy_rate": 1.0, "avg_accuracy_score": 80.0, "avg_confidence": 60.0}}, "confidence_analysis": {"mean": 60.0, "median": 60.0, "std_dev": 0, "min": 60.0, "max": 60.0, "distribution": {"low (0-33)": 0, "medium (34-66)": 1, "high (67-100)": 0}}, "signal_type_performance": {"neutral": {"count": 1, "avg_confidence": 60.0}}, "display_name": "Technical Analyst"}, {"agent_name": "warren_buffett_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "ben_graham_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "peter_lynch_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "bill_ackman_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "cathie_wood_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON><PERSON><PERSON>", "total_evaluations": 0}, {"agent_name": "charlie_munger_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "phil_fisher_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "mi<PERSON><PERSON>_burry_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "stanley_drucken<PERSON>er_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON>", "total_evaluations": 0}, {"agent_name": "aswath_damodaran_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "<PERSON><PERSON><PERSON>", "total_evaluations": 0}, {"agent_name": "fundamentals_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "Fundamentals", "total_evaluations": 0}, {"agent_name": "sentiment_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "Sentiment", "total_evaluations": 0}, {"agent_name": "valuation_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "Valuation", "total_evaluations": 0}, {"agent_name": "factual_news_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "Factual News", "total_evaluations": 0}, {"agent_name": "subjective_news_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "display_name": "Subjective News", "total_evaluations": 0}], "market_analysis": {"error": "No market condition data available"}, "time_horizon_analysis": {"1天": {"days": 1, "average_accuracy": 1.0, "total_evaluations": 1, "top_performers": [{"agent_name": "technical_analyst_agent", "total_signals": 1, "total_evaluations": 1, "overall_accuracy": 1.0, "time_horizon_performance": {"1天": {"total_evaluations": 1, "correct_predictions": 1, "accuracy_rate": 1.0, "avg_accuracy_score": 80.0, "avg_confidence": 60.0}}, "confidence_analysis": {"mean": 60.0, "median": 60.0, "std_dev": 0, "min": 60.0, "max": 60.0, "distribution": {"low (0-33)": 0, "medium (34-66)": 1, "high (67-100)": 0}}, "signal_type_performance": {"neutral": {"count": 1, "avg_confidence": 60.0}}, "selected_horizon_accuracy": 1.0, "selected_horizon_evaluations": 1}, {"agent_name": "warren_buffett_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}, {"agent_name": "ben_graham_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}]}, "3天": {"days": 3, "average_accuracy": 0, "total_evaluations": 0, "top_performers": [{"agent_name": "warren_buffett_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}, {"agent_name": "ben_graham_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}, {"agent_name": "peter_lynch_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}]}, "1周": {"days": 7, "average_accuracy": 0, "total_evaluations": 0, "top_performers": [{"agent_name": "warren_buffett_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}, {"agent_name": "ben_graham_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}, {"agent_name": "peter_lynch_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}]}, "1月": {"days": 30, "average_accuracy": 0, "total_evaluations": 0, "top_performers": [{"agent_name": "warren_buffett_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}, {"agent_name": "ben_graham_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}, {"agent_name": "peter_lynch_agent", "total_signals": 0, "overall_accuracy": 0.0, "time_horizon_performance": {}, "confidence_analysis": {}, "signal_type_performance": {}, "selected_horizon_accuracy": 0.0, "selected_horizon_evaluations": 0}]}}, "recommendations": ["🏆 Technical Analyst 表现优异 (准确率: 100.0%)，建议增加其信号权重", "📊 16 个代理的评估样本不足，建议收集更多数据"]}