#!/usr/bin/env python3
"""
准确性评估器 - 负责多时间维度的信号准确性计算和评估
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import statistics
import numpy as np

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.analysis.signal_database import SignalDatabase, SignalRecord, AccuracyRecord
from src.config.signal_tracker_config import get_config
from src.tools.api import get_prices


class AccuracyEvaluator:
    """准确性评估器"""
    
    def __init__(self, database: SignalDatabase):
        self.database = database
        self.config = get_config()
    
    def evaluate_signal_accuracy(self, 
                                signal: SignalRecord, 
                                evaluation_date: str,
                                time_horizon_days: int) -> Optional[AccuracyRecord]:
        """
        评估单个信号的准确性
        
        Args:
            signal: 信号记录
            evaluation_date: 评估日期
            time_horizon_days: 时间维度（天数）
            
        Returns:
            AccuracyRecord: 准确性评估记录
        """
        try:
            # 获取价格数据
            signal_date = datetime.strptime(signal.signal_date, '%Y-%m-%d')
            eval_date = datetime.strptime(evaluation_date, '%Y-%m-%d')
            
            # 确保评估日期在信号日期之后
            if eval_date <= signal_date:
                return None
            
            # 计算实际的时间差
            actual_days = (eval_date - signal_date).days
            if actual_days < time_horizon_days:
                return None  # 还没到评估时间
            
            # 获取价格数据
            start_date = signal_date.strftime('%Y-%m-%d')
            end_date = eval_date.strftime('%Y-%m-%d')
            
            prices = get_prices(signal.ticker, start_date, end_date)
            if len(prices) < 2:
                print(f"⚠️  价格数据不足: {signal.ticker} {start_date} - {end_date}")
                return None
            
            # 找到信号日期和评估日期的价格
            signal_price = None
            eval_price = None
            
            # 按日期排序价格数据
            prices_dict = {p.time: p.close for p in prices}
            
            # 找到最接近信号日期的价格
            for price_date, price in prices_dict.items():
                if price_date >= start_date and signal_price is None:
                    signal_price = price
                    break
            
            # 找到评估日期的价格
            eval_price = prices_dict.get(end_date)
            if eval_price is None:
                # 如果没有确切日期的价格，找最接近的
                for price_date in sorted(prices_dict.keys(), reverse=True):
                    if price_date <= end_date:
                        eval_price = prices_dict[price_date]
                        break
            
            if signal_price is None or eval_price is None:
                print(f"⚠️  无法获取价格数据: 信号价格={signal_price}, 评估价格={eval_price}")
                return None
            
            # 计算价格变化
            price_change = eval_price - signal_price
            price_change_pct = price_change / signal_price
            
            # 评估准确性
            is_correct = self._evaluate_signal_correctness(
                signal.signal_type, 
                price_change_pct
            )
            
            # 计算准确性分数（考虑置信度）
            accuracy_score = self._calculate_accuracy_score(
                signal.signal_type,
                signal.confidence,
                price_change_pct,
                is_correct
            )
            
            # 创建准确性记录
            accuracy_record = AccuracyRecord(
                signal_id=signal.id,
                evaluation_date=evaluation_date,
                time_horizon_days=time_horizon_days,
                actual_price=eval_price,
                price_change=price_change,
                price_change_pct=price_change_pct,
                is_correct=is_correct,
                accuracy_score=accuracy_score,
                market_condition_at_eval="",  # 将由市场条件分析器填充
                created_at=datetime.now().isoformat()
            )
            
            return accuracy_record
            
        except Exception as e:
            print(f"❌ 评估信号准确性失败: {e}")
            return None
    
    def _evaluate_signal_correctness(self, signal_type: str, price_change_pct: float) -> bool:
        """
        评估信号是否正确
        
        Args:
            signal_type: 信号类型 (bullish, bearish, neutral)
            price_change_pct: 价格变化百分比
            
        Returns:
            bool: 是否正确
        """
        threshold = self.config.accuracy_config.price_change_threshold
        
        if signal_type == "bullish":
            return price_change_pct > 0
        elif signal_type == "bearish":
            return price_change_pct < 0
        elif signal_type == "neutral":
            return abs(price_change_pct) <= threshold
        else:
            return False
    
    def _calculate_accuracy_score(self, 
                                 signal_type: str, 
                                 confidence: float,
                                 price_change_pct: float, 
                                 is_correct: bool) -> float:
        """
        计算准确性分数（0-100）
        
        Args:
            signal_type: 信号类型
            confidence: 置信度
            price_change_pct: 价格变化百分比
            is_correct: 是否正确
            
        Returns:
            float: 准确性分数
        """
        if not is_correct:
            return 0.0
        
        # 基础分数
        base_score = 100.0
        
        # 如果启用置信度加权
        if self.config.accuracy_config.confidence_weight:
            # 置信度越高，分数权重越大
            confidence_weight = confidence / 100.0
            base_score *= confidence_weight
        
        # 根据价格变化幅度调整分数
        abs_change = abs(price_change_pct)
        
        if signal_type in ["bullish", "bearish"]:
            # 对于方向性信号，变化幅度越大，分数越高
            magnitude_bonus = min(abs_change * 100, 50)  # 最多加50分
            base_score += magnitude_bonus
        else:  # neutral
            # 对于中性信号，变化幅度越小，分数越高
            if abs_change <= 0.01:  # 1%以内
                base_score += 20
            elif abs_change <= 0.02:  # 2%以内
                base_score += 10
        
        return min(base_score, 100.0)
    
    def batch_evaluate_signals(self, 
                              evaluation_date: str,
                              time_horizon_days: int,
                              agent_names: Optional[List[str]] = None) -> List[AccuracyRecord]:
        """
        批量评估信号准确性
        
        Args:
            evaluation_date: 评估日期
            time_horizon_days: 时间维度
            agent_names: 要评估的代理名称列表
            
        Returns:
            List[AccuracyRecord]: 准确性评估记录列表
        """
        # 计算信号日期范围
        eval_date = datetime.strptime(evaluation_date, '%Y-%m-%d')
        signal_date = (eval_date - timedelta(days=time_horizon_days)).strftime('%Y-%m-%d')
        
        # 获取需要评估的信号
        signals = self.database.get_signals(
            start_date=signal_date,
            end_date=signal_date,
            ticker=self.config.system_config['target_ticker']
        )
        
        # 过滤代理
        if agent_names:
            signals = [s for s in signals if s.agent_name in agent_names]
        
        accuracy_records = []
        
        for signal in signals:
            # 检查是否已经评估过
            existing_evaluations = self.database.get_accuracy_stats(
                agent_name=signal.agent_name,
                time_horizon_days=time_horizon_days
            )
            
            # 评估信号
            accuracy_record = self.evaluate_signal_accuracy(
                signal, evaluation_date, time_horizon_days
            )
            
            if accuracy_record:
                # 保存到数据库
                accuracy_id = self.database.insert_accuracy_evaluation(accuracy_record)
                if accuracy_id:
                    accuracy_record.id = accuracy_id
                    accuracy_records.append(accuracy_record)
        
        return accuracy_records
    
    def calculate_agent_performance(self, 
                                   agent_name: str,
                                   start_date: Optional[str] = None,
                                   end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        计算代理的整体表现
        
        Args:
            agent_name: 代理名称
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 代理表现统计
        """
        stats = self.database.get_accuracy_stats(
            agent_name=agent_name,
            start_date=start_date,
            end_date=end_date
        )
        
        if agent_name not in stats:
            return {
                'agent_name': agent_name,
                'total_signals': 0,
                'overall_accuracy': 0.0,
                'time_horizon_performance': {},
                'confidence_analysis': {},
                'signal_type_performance': {}
            }
        
        agent_stats = stats[agent_name]
        
        # 计算整体准确率
        total_evaluations = sum(horizon['total_evaluations'] for horizon in agent_stats.values())
        total_correct = sum(horizon['correct_predictions'] for horizon in agent_stats.values())
        overall_accuracy = total_correct / total_evaluations if total_evaluations > 0 else 0.0
        
        # 获取详细信号数据进行分析
        signals = self.database.get_signals(
            agent_name=agent_name,
            start_date=start_date,
            end_date=end_date,
            ticker=self.config.system_config['target_ticker']
        )
        
        # 分析置信度分布
        confidence_analysis = self._analyze_confidence_distribution(signals)
        
        # 分析信号类型表现
        signal_type_performance = self._analyze_signal_type_performance(agent_name, signals)
        
        return {
            'agent_name': agent_name,
            'total_signals': len(signals),
            'total_evaluations': total_evaluations,
            'overall_accuracy': overall_accuracy,
            'time_horizon_performance': agent_stats,
            'confidence_analysis': confidence_analysis,
            'signal_type_performance': signal_type_performance
        }
    
    def _analyze_confidence_distribution(self, signals: List[SignalRecord]) -> Dict[str, Any]:
        """分析置信度分布"""
        if not signals:
            return {}
        
        confidences = [s.confidence for s in signals]
        
        return {
            'mean': statistics.mean(confidences),
            'median': statistics.median(confidences),
            'std_dev': statistics.stdev(confidences) if len(confidences) > 1 else 0,
            'min': min(confidences),
            'max': max(confidences),
            'distribution': {
                'low (0-33)': len([c for c in confidences if c <= 33]),
                'medium (34-66)': len([c for c in confidences if 34 <= c <= 66]),
                'high (67-100)': len([c for c in confidences if c >= 67])
            }
        }
    
    def _analyze_signal_type_performance(self, 
                                       agent_name: str, 
                                       signals: List[SignalRecord]) -> Dict[str, Any]:
        """分析信号类型表现"""
        signal_types = {}
        
        for signal in signals:
            signal_type = signal.signal_type
            if signal_type not in signal_types:
                signal_types[signal_type] = {
                    'count': 0,
                    'avg_confidence': 0.0,
                    'confidences': []
                }
            
            signal_types[signal_type]['count'] += 1
            signal_types[signal_type]['confidences'].append(signal.confidence)
        
        # 计算平均置信度
        for signal_type, data in signal_types.items():
            if data['confidences']:
                data['avg_confidence'] = statistics.mean(data['confidences'])
                del data['confidences']  # 删除原始数据以节省空间
        
        return signal_types
    
    def get_comparative_performance(self, 
                                   time_horizon_days: int,
                                   start_date: Optional[str] = None,
                                   end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取所有代理的比较表现
        
        Args:
            time_horizon_days: 时间维度
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict]: 按准确率排序的代理表现列表
        """
        enabled_agents = self.config.get_enabled_agents()
        performances = []
        
        for agent_config in enabled_agents:
            performance = self.calculate_agent_performance(
                agent_config.name, start_date, end_date
            )
            
            # 获取特定时间维度的表现
            horizon_key = f"{time_horizon_days}天"
            if horizon_key in performance['time_horizon_performance']:
                horizon_perf = performance['time_horizon_performance'][horizon_key]
                performance['selected_horizon_accuracy'] = horizon_perf['accuracy_rate']
                performance['selected_horizon_evaluations'] = horizon_perf['total_evaluations']
            else:
                performance['selected_horizon_accuracy'] = 0.0
                performance['selected_horizon_evaluations'] = 0
            
            performances.append(performance)
        
        # 按准确率排序
        performances.sort(key=lambda x: x['selected_horizon_accuracy'], reverse=True)
        
        return performances


if __name__ == "__main__":
    # 测试准确性评估器
    from src.analysis.signal_database import SignalDatabase
    
    db = SignalDatabase("test_signal_tracking.db")
    evaluator = AccuracyEvaluator(db)
    
    # 测试批量评估
    evaluation_date = "2025-06-18"
    time_horizon = 1
    
    accuracy_records = evaluator.batch_evaluate_signals(
        evaluation_date, time_horizon
    )
    
    print(f"评估了 {len(accuracy_records)} 个信号")
    
    # 测试代理表现分析
    performance = evaluator.calculate_agent_performance("warren_buffett_agent")
    print(f"Warren Buffett 代理表现: {performance}")
