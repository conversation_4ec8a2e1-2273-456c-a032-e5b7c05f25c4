#!/usr/bin/env python3
"""
市场条件分析器 - 负责检测和分析市场环境条件
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import statistics
import numpy as np

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.config.signal_tracker_config import get_config
from src.tools.api import get_prices


class MarketConditionAnalyzer:
    """市场条件分析器"""
    
    def __init__(self):
        self.config = get_config()
    
    def analyze_market_condition(self, 
                               ticker: str, 
                               date: str,
                               lookback_days: Optional[int] = None) -> Dict[str, Any]:
        """
        分析指定日期的市场条件
        
        Args:
            ticker: 股票代码
            date: 分析日期
            lookback_days: 回看天数，默认使用配置值
            
        Returns:
            Dict: 市场条件分析结果
        """
        if lookback_days is None:
            lookback_days = max(
                self.config.market_condition_config.volatility_window,
                self.config.market_condition_config.trend_window
            )
        
        try:
            # 获取价格数据
            end_date = datetime.strptime(date, '%Y-%m-%d')
            start_date = end_date - timedelta(days=lookback_days + 10)  # 额外天数确保有足够数据
            
            prices = get_prices(
                ticker, 
                start_date.strftime('%Y-%m-%d'), 
                end_date.strftime('%Y-%m-%d')
            )
            
            if len(prices) < lookback_days // 2:
                return self._create_default_condition("insufficient_data")
            
            # 转换为价格序列
            price_data = [(p.time, p.close, p.volume if hasattr(p, 'volume') else 0) for p in prices]
            price_data.sort(key=lambda x: x[0])  # 按日期排序
            
            # 提取收盘价和成交量
            closes = [p[1] for p in price_data]
            volumes = [p[2] for p in price_data]
            dates = [p[0] for p in price_data]
            
            # 找到目标日期的索引
            target_index = None
            for i, price_date in enumerate(dates):
                if price_date <= date:
                    target_index = i
            
            if target_index is None or target_index < lookback_days:
                return self._create_default_condition("insufficient_historical_data")
            
            # 计算各种市场指标
            volatility = self._calculate_volatility(closes, target_index)
            trend_info = self._calculate_trend(closes, target_index)
            volume_info = self._calculate_volume_analysis(volumes, target_index)
            
            # 确定市场条件类型
            condition_type = self._determine_market_condition(volatility, trend_info)
            
            return {
                'date': date,
                'ticker': ticker,
                'condition_type': condition_type,
                'volatility': volatility,
                'trend_direction': trend_info['direction'],
                'trend_strength': trend_info['strength'],
                'price': closes[target_index],
                'volume': volumes[target_index] if volumes else 0,
                'analysis_details': {
                    'volatility_percentile': volatility['percentile'],
                    'trend_slope': trend_info['slope'],
                    'volume_ratio': volume_info['volume_ratio'],
                    'price_momentum': trend_info['momentum']
                }
            }
            
        except Exception as e:
            print(f"❌ 市场条件分析失败: {e}")
            return self._create_default_condition("analysis_error")
    
    def _calculate_volatility(self, closes: List[float], target_index: int) -> Dict[str, Any]:
        """计算波动率"""
        window = self.config.market_condition_config.volatility_window
        
        if target_index < window:
            return {'value': 0.0, 'level': 'unknown', 'percentile': 0.0}
        
        # 计算日收益率
        returns = []
        for i in range(target_index - window + 1, target_index + 1):
            if i > 0:
                daily_return = (closes[i] - closes[i-1]) / closes[i-1]
                returns.append(daily_return)
        
        if len(returns) < 2:
            return {'value': 0.0, 'level': 'unknown', 'percentile': 0.0}
        
        # 计算标准差（年化波动率）
        volatility = statistics.stdev(returns) * (252 ** 0.5)  # 年化
        
        # 计算历史百分位
        historical_volatilities = []
        for i in range(window, target_index + 1):
            hist_returns = []
            for j in range(i - window + 1, i + 1):
                if j > 0:
                    hist_returns.append((closes[j] - closes[j-1]) / closes[j-1])
            if len(hist_returns) >= 2:
                hist_vol = statistics.stdev(hist_returns) * (252 ** 0.5)
                historical_volatilities.append(hist_vol)
        
        percentile = 0.0
        if historical_volatilities:
            percentile = sum(1 for v in historical_volatilities if v <= volatility) / len(historical_volatilities) * 100
        
        # 确定波动率水平
        if percentile >= 80:
            level = 'very_high'
        elif percentile >= 60:
            level = 'high'
        elif percentile >= 40:
            level = 'medium'
        elif percentile >= 20:
            level = 'low'
        else:
            level = 'very_low'
        
        return {
            'value': volatility,
            'level': level,
            'percentile': percentile
        }
    
    def _calculate_trend(self, closes: List[float], target_index: int) -> Dict[str, Any]:
        """计算趋势信息"""
        window = self.config.market_condition_config.trend_window
        
        if target_index < window:
            return {
                'direction': 'unknown',
                'strength': 0.0,
                'slope': 0.0,
                'momentum': 0.0
            }
        
        # 获取趋势窗口内的价格
        trend_prices = closes[target_index - window + 1:target_index + 1]
        
        # 计算线性回归斜率
        x = list(range(len(trend_prices)))
        n = len(x)
        
        sum_x = sum(x)
        sum_y = sum(trend_prices)
        sum_xy = sum(x[i] * trend_prices[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        # 计算R²（趋势强度）
        y_mean = sum_y / n
        ss_tot = sum((trend_prices[i] - y_mean) ** 2 for i in range(n))
        ss_res = sum((trend_prices[i] - (slope * x[i] + (sum_y - slope * sum_x) / n)) ** 2 for i in range(n))
        
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # 计算价格动量（短期vs长期移动平均）
        short_ma = statistics.mean(closes[target_index - 9:target_index + 1])  # 10日均线
        long_ma = statistics.mean(closes[target_index - 19:target_index + 1])   # 20日均线
        momentum = (short_ma - long_ma) / long_ma if long_ma != 0 else 0
        
        # 确定趋势方向
        bull_threshold = self.config.market_condition_config.bull_threshold
        bear_threshold = self.config.market_condition_config.bear_threshold
        
        if momentum > bull_threshold:
            direction = 'bullish'
        elif momentum < bear_threshold:
            direction = 'bearish'
        else:
            direction = 'sideways'
        
        return {
            'direction': direction,
            'strength': r_squared,
            'slope': slope,
            'momentum': momentum
        }
    
    def _calculate_volume_analysis(self, volumes: List[float], target_index: int) -> Dict[str, Any]:
        """计算成交量分析"""
        if not volumes or target_index < 20:
            return {'volume_ratio': 1.0, 'volume_trend': 'unknown'}
        
        # 计算成交量比率（当前vs平均）
        current_volume = volumes[target_index]
        avg_volume = statistics.mean(volumes[max(0, target_index - 19):target_index + 1])
        
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # 确定成交量趋势
        if volume_ratio >= 1.5:
            volume_trend = 'high'
        elif volume_ratio >= 1.2:
            volume_trend = 'above_average'
        elif volume_ratio >= 0.8:
            volume_trend = 'normal'
        else:
            volume_trend = 'low'
        
        return {
            'volume_ratio': volume_ratio,
            'volume_trend': volume_trend
        }
    
    def _determine_market_condition(self, 
                                  volatility: Dict[str, Any], 
                                  trend_info: Dict[str, Any]) -> str:
        """确定市场条件类型"""
        vol_level = volatility['level']
        trend_direction = trend_info['direction']
        trend_strength = trend_info['strength']
        
        # 基于波动率和趋势确定市场条件
        if vol_level in ['very_high', 'high']:
            if trend_direction == 'bearish' and trend_strength > 0.5:
                return 'bear_volatile'
            elif trend_direction == 'bullish' and trend_strength > 0.5:
                return 'bull_volatile'
            else:
                return 'volatile_sideways'
        
        elif vol_level in ['very_low', 'low']:
            if trend_direction == 'bearish':
                return 'bear_stable'
            elif trend_direction == 'bullish':
                return 'bull_stable'
            else:
                return 'stable_sideways'
        
        else:  # medium volatility
            if trend_direction == 'bearish':
                return 'bear_normal'
            elif trend_direction == 'bullish':
                return 'bull_normal'
            else:
                return 'normal_sideways'
    
    def _create_default_condition(self, reason: str) -> Dict[str, Any]:
        """创建默认市场条件"""
        return {
            'date': '',
            'ticker': '',
            'condition_type': 'unknown',
            'volatility': {'value': 0.0, 'level': 'unknown', 'percentile': 0.0},
            'trend_direction': 'unknown',
            'trend_strength': 0.0,
            'price': 0.0,
            'volume': 0.0,
            'analysis_details': {
                'error_reason': reason,
                'volatility_percentile': 0.0,
                'trend_slope': 0.0,
                'volume_ratio': 1.0,
                'price_momentum': 0.0
            }
        }
    
    def batch_analyze_market_conditions(self, 
                                      ticker: str,
                                      start_date: str,
                                      end_date: str) -> List[Dict[str, Any]]:
        """
        批量分析市场条件
        
        Args:
            ticker: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict]: 市场条件分析结果列表
        """
        results = []
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_dt = start_dt
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y-%m-%d')
            
            condition = self.analyze_market_condition(ticker, date_str)
            if condition['condition_type'] != 'unknown':
                results.append(condition)
            
            current_dt += timedelta(days=1)
        
        return results
    
    def get_market_condition_summary(self, 
                                   ticker: str,
                                   start_date: str,
                                   end_date: str) -> Dict[str, Any]:
        """
        获取市场条件摘要统计
        
        Args:
            ticker: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 市场条件摘要
        """
        conditions = self.batch_analyze_market_conditions(ticker, start_date, end_date)
        
        if not conditions:
            return {'error': 'No market condition data available'}
        
        # 统计各种市场条件的分布
        condition_counts = {}
        volatility_levels = {}
        trend_directions = {}
        
        for condition in conditions:
            # 市场条件类型统计
            cond_type = condition['condition_type']
            condition_counts[cond_type] = condition_counts.get(cond_type, 0) + 1
            
            # 波动率水平统计
            vol_level = condition['volatility']['level']
            volatility_levels[vol_level] = volatility_levels.get(vol_level, 0) + 1
            
            # 趋势方向统计
            trend_dir = condition['trend_direction']
            trend_directions[trend_dir] = trend_directions.get(trend_dir, 0) + 1
        
        total_days = len(conditions)
        
        # 计算百分比
        condition_percentages = {k: (v / total_days * 100) for k, v in condition_counts.items()}
        volatility_percentages = {k: (v / total_days * 100) for k, v in volatility_levels.items()}
        trend_percentages = {k: (v / total_days * 100) for k, v in trend_directions.items()}
        
        # 计算平均波动率
        avg_volatility = statistics.mean([c['volatility']['value'] for c in conditions])
        
        return {
            'period': f"{start_date} to {end_date}",
            'total_days_analyzed': total_days,
            'condition_distribution': condition_percentages,
            'volatility_distribution': volatility_percentages,
            'trend_distribution': trend_percentages,
            'average_volatility': avg_volatility,
            'most_common_condition': max(condition_counts, key=condition_counts.get),
            'most_common_trend': max(trend_directions, key=trend_directions.get)
        }


if __name__ == "__main__":
    # 测试市场条件分析器
    analyzer = MarketConditionAnalyzer()
    
    # 测试单日分析
    condition = analyzer.analyze_market_condition("AAPL", "2025-06-18")
    print(f"市场条件分析: {condition}")
    
    # 测试批量分析
    conditions = analyzer.batch_analyze_market_conditions(
        "AAPL", "2025-06-01", "2025-06-18"
    )
    print(f"批量分析了 {len(conditions)} 天的市场条件")
    
    # 测试摘要统计
    summary = analyzer.get_market_condition_summary(
        "AAPL", "2025-06-01", "2025-06-18"
    )
    print(f"市场条件摘要: {summary}")
