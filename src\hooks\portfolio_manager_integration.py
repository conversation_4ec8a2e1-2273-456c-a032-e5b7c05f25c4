#!/usr/bin/env python3
"""
投资组合管理器集成钩子 - 在投资组合管理器中集成信号跟踪功能
"""

import os
import sys
from typing import Dict, Any

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.hooks.signal_interceptor import get_signal_interceptor
from src.config.signal_tracker_config import get_config


def integrate_signal_tracking_to_portfolio_manager():
    """
    将信号跟踪功能集成到投资组合管理器中
    
    这个函数修改投资组合管理器的行为，在处理代理信号之前拦截并跟踪它们。
    """
    try:
        # 导入投资组合管理器模块
        from src.agents import portfolio_manager
        
        # 保存原始的generate_trading_decision函数
        original_generate_trading_decision = portfolio_manager.generate_trading_decision
        
        def enhanced_generate_trading_decision(
            tickers,
            signals_by_ticker,
            current_prices,
            max_shares,
            portfolio,
            previous_reflections,
            model_name,
            model_provider,
        ):
            """
            增强的交易决策生成函数，集成信号跟踪
            """
            # 在处理信号之前拦截并跟踪
            config = get_config()
            if config.system_config.get('enabled', True):
                try:
                    interceptor = get_signal_interceptor()
                    interceptor.intercept_portfolio_manager_signals(signals_by_ticker)
                except Exception as e:
                    print(f"⚠️  信号拦截失败: {e}")
            
            # 调用原始的交易决策函数
            return original_generate_trading_decision(
                tickers,
                signals_by_ticker,
                current_prices,
                max_shares,
                portfolio,
                previous_reflections,
                model_name,
                model_provider,
            )
        
        # 替换原始函数
        portfolio_manager.generate_trading_decision = enhanced_generate_trading_decision
        
        print("✅ 信号跟踪已集成到投资组合管理器")
        return True
        
    except Exception as e:
        print(f"❌ 集成信号跟踪失败: {e}")
        return False


def integrate_signal_tracking_to_main():
    """
    将信号跟踪功能集成到主运行流程中
    """
    try:
        # 导入主模块
        from src import main
        
        # 保存原始的run_hedge_fund函数
        original_run_hedge_fund = main.run_hedge_fund
        
        def enhanced_run_hedge_fund(
            tickers,
            start_date,
            end_date,
            portfolio,
            show_reasoning=False,
            selected_analysts=[],
            model_name="gpt-4o",
            model_provider="OpenAI",
        ):
            """
            增强的对冲基金运行函数，集成信号跟踪
            """
            print("🎯 启动增强版对冲基金 (集成信号跟踪)")
            
            # 调用原始函数
            result = original_run_hedge_fund(
                tickers,
                start_date,
                end_date,
                portfolio,
                show_reasoning,
                selected_analysts,
                model_name,
                model_provider,
            )
            
            # 在结果中添加信号跟踪信息
            config = get_config()
            if config.system_config.get('enabled', True):
                try:
                    interceptor = get_signal_interceptor()
                    
                    # 获取最近的信号统计
                    recent_signals = interceptor.get_recent_signals(24)
                    
                    # 添加跟踪信息到结果中
                    if 'tracking_info' not in result:
                        result['tracking_info'] = {}
                    
                    result['tracking_info'].update({
                        'signals_tracked': len(recent_signals),
                        'tracking_enabled': True,
                        'target_ticker': config.system_config['target_ticker']
                    })
                    
                    print(f"📊 信号跟踪统计: 最近24小时跟踪了 {len(recent_signals)} 个信号")
                    
                except Exception as e:
                    print(f"⚠️  获取跟踪统计失败: {e}")
                    result['tracking_info'] = {'tracking_enabled': False, 'error': str(e)}
            
            return result
        
        # 替换原始函数
        main.run_hedge_fund = enhanced_run_hedge_fund
        
        print("✅ 信号跟踪已集成到主运行流程")
        return True
        
    except Exception as e:
        print(f"❌ 集成主流程失败: {e}")
        return False


def create_integration_patch():
    """
    创建集成补丁文件，用于手动集成
    """
    patch_content = '''
# AI对冲基金信号跟踪集成补丁
# 
# 将以下代码添加到 src/agents/portfolio_manager.py 的开头：

from src.hooks.signal_interceptor import get_signal_interceptor
from src.config.signal_tracker_config import get_config

# 在 generate_trading_decision 函数的开头添加：

def generate_trading_decision(
    tickers: list[str],
    signals_by_ticker: dict[str, dict],
    current_prices: dict[str, float],
    max_shares: dict[str, int],
    portfolio: dict[str, float],
    previous_reflections: dict[str, dict],
    model_name: str,
    model_provider: str,
) -> PortfolioManagerOutput:
    """Attempts to get a decision from the LLM with retry logic"""
    
    # === 信号跟踪集成代码开始 ===
    config = get_config()
    if config.system_config.get('enabled', True):
        try:
            interceptor = get_signal_interceptor()
            interceptor.intercept_portfolio_manager_signals(signals_by_ticker)
        except Exception as e:
            print(f"⚠️  信号拦截失败: {e}")
    # === 信号跟踪集成代码结束 ===
    
    # 原有的函数逻辑继续...
    template = ChatPromptTemplate.from_messages([
        # ... 原有代码
    ])
'''
    
    try:
        with open('signal_tracking_integration_patch.txt', 'w', encoding='utf-8') as f:
            f.write(patch_content)
        
        print("✅ 集成补丁文件已创建: signal_tracking_integration_patch.txt")
        return True
        
    except Exception as e:
        print(f"❌ 创建补丁文件失败: {e}")
        return False


def auto_integrate():
    """
    自动集成信号跟踪功能
    """
    print("🔧 开始自动集成信号跟踪功能...")
    
    success_count = 0
    total_integrations = 2
    
    # 集成到投资组合管理器
    if integrate_signal_tracking_to_portfolio_manager():
        success_count += 1
    
    # 集成到主流程
    if integrate_signal_tracking_to_main():
        success_count += 1
    
    if success_count == total_integrations:
        print("🎉 所有集成完成！信号跟踪系统已成功集成到AI对冲基金系统中")
        print("💡 现在运行主系统时会自动跟踪代理信号")
        return True
    else:
        print(f"⚠️  部分集成失败 ({success_count}/{total_integrations})")
        print("💡 您可以使用手动集成补丁文件")
        create_integration_patch()
        return False


def test_integration():
    """
    测试集成是否成功
    """
    print("🧪 测试信号跟踪集成...")
    
    try:
        # 测试配置加载
        config = get_config()
        print(f"✅ 配置加载成功: {config.system_config['target_ticker']}")
        
        # 测试信号拦截器
        interceptor = get_signal_interceptor()
        print("✅ 信号拦截器初始化成功")
        
        # 测试数据库连接
        db_stats = interceptor.database.get_database_stats()
        print(f"✅ 数据库连接成功: {db_stats.get('signal_records', 0)} 条信号记录")
        
        # 测试模拟信号拦截
        test_signals = {
            'AAPL': {
                'warren_buffett_agent': {
                    'signal': 'bullish',
                    'confidence': 75.0,
                    'reasoning': 'Integration test signal'
                }
            }
        }
        
        success = interceptor.intercept_portfolio_manager_signals(test_signals)
        if success:
            print("✅ 信号拦截测试成功")
        else:
            print("⚠️  信号拦截测试失败")
        
        print("🎉 集成测试完成！系统运行正常")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def print_integration_status():
    """
    打印集成状态
    """
    print("\n" + "="*60)
    print("🔧 信号跟踪系统集成状态")
    print("="*60)
    
    try:
        config = get_config()
        print(f"⚙️  系统状态: {'启用' if config.system_config.get('enabled', True) else '禁用'}")
        print(f"🎯 目标股票: {config.system_config.get('target_ticker', 'AAPL')}")
        print(f"👥 跟踪代理: {len(config.get_enabled_agents())}/{len(config.agents)}")
        print(f"⏰ 时间维度: {len(config.get_enabled_time_horizons())}/{len(config.time_horizons)}")
        
        # 检查数据库
        interceptor = get_signal_interceptor()
        db_stats = interceptor.database.get_database_stats()
        print(f"💾 信号记录: {db_stats.get('signal_records', 0)}")
        print(f"📈 准确性评估: {db_stats.get('accuracy_evaluations', 0)}")
        
        # 检查最近活动
        recent_signals = interceptor.get_recent_signals(24)
        print(f"🕐 最近24小时: {len(recent_signals)} 个新信号")
        
        print("✅ 集成状态: 正常运行")
        
    except Exception as e:
        print(f"❌ 集成状态检查失败: {e}")
    
    print("="*60)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="信号跟踪系统集成工具")
    parser.add_argument('--auto-integrate', action='store_true', help='自动集成信号跟踪')
    parser.add_argument('--test', action='store_true', help='测试集成状态')
    parser.add_argument('--status', action='store_true', help='显示集成状态')
    parser.add_argument('--create-patch', action='store_true', help='创建手动集成补丁')
    
    args = parser.parse_args()
    
    if args.auto_integrate:
        auto_integrate()
    elif args.test:
        test_integration()
    elif args.status:
        print_integration_status()
    elif args.create_patch:
        create_integration_patch()
    else:
        print("🔧 信号跟踪系统集成工具")
        print("\n可用选项:")
        print("  --auto-integrate  自动集成信号跟踪功能")
        print("  --test           测试集成状态")
        print("  --status         显示集成状态")
        print("  --create-patch   创建手动集成补丁")
        print("\n示例:")
        print("  python src/hooks/portfolio_manager_integration.py --auto-integrate")
        print("  python src/hooks/portfolio_manager_integration.py --test")
